import React from "react";
const Services = () => {
  const services = [
    {
      title: "Temporary Staffing",
      icon: "fa-bolt",
      description:
        "Quickly fill short-term needs with our pre-screened temporary workers available for immediate placement.",
      features: [
        "24-48 hour placement guarantee",
        "Flexible durations from 1 day to 6 months",
        "Pre-screened and qualified candidates",
        "Workers' compensation coverage",
        "Payroll and tax management",
      ],
      bestFor:
        "Seasonal peaks, special projects, unexpected demand, employee absences",
    },
    {
      title: "Direct Hire Placement",
      icon: "fa-calendar",
      description:
        "Find permanent employees through our rigorous recruitment and vetting process.",
      features: [
        "90-day replacement guarantee",
        "Comprehensive skills testing",
        "Background and reference checks",
        "Cultural fit assessment",
        "Competitive salary benchmarking",
      ],
      bestFor: "Long-term staffing needs, key positions, leadership roles",
    },
    {
      title: "Temp-to-Hire",
      icon: "fa-exchange",
      description:
        "Evaluate workers on the job before making a permanent hiring decision.",
      features: [
        "Low-risk trial period",
        "Seamless transition to permanent",
        "Reduced recruiting costs",
        "Performance evaluations",
        "Flexible conversion timelines",
      ],
      bestFor: "When you want to evaluate fit before permanent commitment",
    },
    {
      title: "On-Site Management",
      icon: "fa-building",
      description:
        "Our dedicated managers oversee your temporary workforce at your facility.",
      features: [
        "Dedicated account manager",
        "Daily supervision",
        "Time and attendance tracking",
        "Performance management",
        "Continuous recruitment",
      ],
      bestFor: "Large-scale operations, high-volume staffing needs",
    },
    {
      title: "Payroll Services",
      icon: "fa-calculator",
      description:
        "Outsource your workforce payroll and administrative tasks to our experts.",
      features: [
        "Complete payroll processing",
        "Tax filing and compliance",
        "Benefits administration",
        "Workers' compensation",
        "Detailed reporting",
      ],
      bestFor: "Companies wanting to reduce HR overhead",
    },
    {
      title: "Specialized Recruitment",
      icon: "fa-search-plus",
      description:
        "Targeted search for hard-to-find warehouse and logistics specialists.",
      features: [
        "Niche talent networks",
        "Competitive market analysis",
        "Passive candidate outreach",
        "Skills testing",
        "Exclusive candidate access",
      ],
      bestFor: "Hard-to-fill positions, specialized skills, leadership roles",
    },
  ];

  return (
    <div className="services-page">
      {/* Hero Section */}
      <section className="hero-section bg-primary text-white py-5">
        <div className="container py-5">
          <div className="row align-items-center">
            <div className="col-lg-6 mb-4 mb-lg-0">
              <h1 className="display-4 fw-bold mb-4">Our Staffing Services</h1>
              <p className="lead">
                Comprehensive solutions tailored to your warehouse and logistics
                staffing needs
              </p>
            </div>
            <div className="col-lg-6">
              <img
                src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                alt="Professional staffing services for warehouse and logistics operations"
                className="img-fluid rounded shadow-lg opacity-90"
              />
            </div>
          </div>
        </div>
      </section>

      {/* All Services */}
      <section className="py-5">
        <div className="container">
          <div className="text-center mb-5">
            <h2 className="fw-bold">Complete Staffing Solutions</h2>
            <p className="lead text-muted">
              Flexible options to meet your specific requirements
            </p>
          </div>
          <div className="row g-4">
            {services.map((service, index) => (
              <div key={index} className="col-lg-4 col-md-6">
                {/* <ServiceCard service={service} /> */}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="text-center mb-5">
            <h2 className="fw-bold">Our Proven Process</h2>
            <p className="lead text-muted">
              How we deliver exceptional staffing results
            </p>
          </div>
          <div className="row g-4">
            {[
              {
                step: "1",
                title: "Needs Assessment",
                desc: "We thoroughly understand your specific requirements, culture, and challenges",
              },
              {
                step: "2",
                title: "Talent Sourcing",
                desc: "We tap into our extensive network and use targeted recruitment strategies",
              },
              {
                step: "3",
                title: "Rigorous Screening",
                desc: "Candidates undergo skills testing, interviews, and background checks",
              },
              {
                step: "4",
                title: "Presentation",
                desc: "We present only the most qualified candidates that match your needs",
              },
              {
                step: "5",
                title: "Placement",
                desc: "We facilitate interviews and handle all onboarding logistics",
              },
              {
                step: "6",
                title: "Ongoing Support",
                desc: "We provide continuous support to ensure satisfaction on both sides",
              },
            ].map((item, index) => (
              <div key={index} className="col-md-4 col-lg-2">
                <div className="card h-100 border-0 bg-white text-center p-3 hover-effect">
                  <div
                    className="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3 mx-auto"
                    style={{ width: "50px", height: "50px" }}
                  >
                    <span className="fw-bold">{item.step}</span>
                  </div>
                  <h6 className="mb-0">{item.title}</h6>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-5 bg-primary text-white">
        <div className="container text-center py-4">
          <h2 className="fw-bold mb-4">
            Ready to Solve Your Staffing Challenges?
          </h2>
          <p className="lead mb-5">
            Our experts are ready to discuss your specific needs and recommend
            the best solutions.
          </p>
          <div className="d-flex flex-wrap justify-content-center gap-3">
            <a href="/contact" className="btn btn-light btn-lg px-4">
              <i className="fa fa-phone me-2"></i> Get Started
            </a>
            <a href="/jobs" className="btn btn-outline-light btn-lg px-4">
              <i className="fa fa-file-text me-2"></i> Request a Proposal
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Services;
