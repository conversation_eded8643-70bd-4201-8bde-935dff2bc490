import React from "react";

const Alert = ({ type, message }) => {
  const alertClasses = {
    success: "alert-success",
    danger: "alert-danger",
    warning: "alert-warning",
    info: "alert-info",
  };

  return (
    <div
      className={`alert ${
        alertClasses[type] || "alert-info"
      } alert-dismissible fade show`}
      role="alert"
    >
      {message}
      <button
        type="button"
        className="btn-close"
        data-bs-dismiss="alert"
        aria-label="Close"
      ></button>
    </div>
  );
};

export default Alert;
