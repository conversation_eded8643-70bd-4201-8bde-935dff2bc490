/* Import Bootstrap and <PERSON>ont Awesome */
@import "~bootstrap/dist/css/bootstrap.min.css";
@import "~font-awesome/css/font-awesome.min.css";

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.hero-section {
  background-color: #0d6efd;
  color: white;
  padding: 4rem 0;
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #0d6efd;
}

.job-card {
  transition: transform 0.3s ease;
  margin-bottom: 20px;
}

.job-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.footer {
  background-color: #f8f9fa;
  padding: 2rem 0;
}

/* Job card hover effect */
.hover-effect {
  transition: all 0.3s ease;
}

.hover-effect:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
}

/* Badge styling override */
.badge {
  font-weight: 500;
  padding: 0.35em 0.65em;
}

/* Light badge variant */
.badge.bg-light {
  border: 1px solid #dee2e6;
}

/* Job Details */

/* Job details specific styles */
.job-details-page .breadcrumb {
  padding: 0;
  background-color: transparent;
}

.job-details-page .hover-effect {
  transition: all 0.3s ease;
}

.job-details-page .hover-effect:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
}

/* Modal customization */
#applyModal .modal-header {
  border-bottom: none;
}

#applyModal .modal-footer {
  border-top: none;
}
