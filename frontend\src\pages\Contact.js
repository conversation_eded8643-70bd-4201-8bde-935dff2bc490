import React, { useState } from "react";

const Contact = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
    contactMethod: "email",
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // In a real app, this would submit to your backend
    console.log("Form submitted:", formData);
    alert("Thank you for your message! We will contact you soon.");
    setFormData({
      name: "",
      email: "",
      phone: "",
      subject: "",
      message: "",
      contactMethod: "email",
    });
  };

  return (
    <div className="contact-page">
      {/* Hero Section */}
      <section className="hero-section bg-primary text-white py-5">
        <div className="container py-5">
          <div className="row align-items-center">
            <div className="col-lg-6 mb-4 mb-lg-0">
              <h1 className="display-4 fw-bold mb-4">Contact Us</h1>
              <p className="lead">
                We're here to help with all your warehouse staffing needs
              </p>
            </div>
            <div className="col-lg-6">
              <img
                src="/images/contact-hero.jpg"
                alt="Customer service representative"
                className="img-fluid rounded shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-5">
        <div className="container">
          <div className="row">
            <div className="col-lg-6 mb-5 mb-lg-0">
              <div className="card shadow-sm">
                <div className="card-body p-4 p-md-5">
                  <h2 className="fw-bold mb-4">Send Us a Message</h2>
                  <form onSubmit={handleSubmit}>
                    <div className="mb-3">
                      <label htmlFor="name" className="form-label">
                        Full Name
                      </label>
                      <input
                        type="text"
                        className="form-control"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                      />
                    </div>

                    <div className="mb-3">
                      <label htmlFor="email" className="form-label">
                        Email Address
                      </label>
                      <input
                        type="email"
                        className="form-control"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                      />
                    </div>

                    <div className="mb-3">
                      <label htmlFor="phone" className="form-label">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        className="form-control"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                      />
                    </div>

                    <div className="mb-3">
                      <label htmlFor="subject" className="form-label">
                        Subject
                      </label>
                      <select
                        className="form-select"
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleChange}
                        required
                      >
                        <option value="">Select a subject</option>
                        <option value="General Inquiry">General Inquiry</option>
                        <option value="Job Seekers">Job Seekers</option>
                        <option value="Employers">Employers</option>
                        <option value="Support">Support</option>
                        <option value="Feedback">Feedback</option>
                      </select>
                    </div>

                    <div className="mb-4">
                      <label htmlFor="message" className="form-label">
                        Message
                      </label>
                      <textarea
                        className="form-control"
                        id="message"
                        name="message"
                        rows="5"
                        value={formData.message}
                        onChange={handleChange}
                        required
                      ></textarea>
                    </div>

                    <div className="mb-4">
                      <label className="form-label">
                        Preferred Contact Method
                      </label>
                      <div className="d-flex gap-3">
                        <div className="form-check">
                          <input
                            className="form-check-input"
                            type="radio"
                            name="contactMethod"
                            id="contactEmail"
                            value="email"
                            checked={formData.contactMethod === "email"}
                            onChange={handleChange}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="contactEmail"
                          >
                            Email
                          </label>
                        </div>
                        <div className="form-check">
                          <input
                            className="form-check-input"
                            type="radio"
                            name="contactMethod"
                            id="contactPhone"
                            value="phone"
                            checked={formData.contactMethod === "phone"}
                            onChange={handleChange}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="contactPhone"
                          >
                            Phone
                          </label>
                        </div>
                      </div>
                    </div>

                    <button
                      type="submit"
                      className="btn btn-primary w-100 py-2"
                    >
                      <i className="fa fa-paper-plane me-2"></i> Send Message
                    </button>
                  </form>
                </div>
              </div>
            </div>

            <div className="col-lg-6">
              <div className="card shadow-sm h-100">
                <div className="card-body p-4 p-md-5">
                  <h2 className="fw-bold mb-4">Contact Information</h2>

                  <div className="mb-5">
                    <h5 className="mb-3 d-flex align-items-center">
                      <i className="fa fa-map-marker text-primary me-3"></i>
                      Our Offices
                    </h5>
                    <address>
                      <strong>Headquarters</strong>
                      <br />
                      123 Warehouse Drive, Suite 400
                      <br />
                      Chicago, IL 60601
                      <br />
                      <br />
                      <strong>Regional Offices</strong>
                      <br />
                      456 Industrial Parkway, New York, NY 10001
                      <br />
                      789 Distribution Center Blvd, Los Angeles, CA 90001
                      <br />
                      101 Logistics Lane, Dallas, TX 75201
                    </address>
                  </div>

                  <div className="mb-5">
                    <h5 className="mb-3 d-flex align-items-center">
                      <i className="fa fa-phone text-primary me-3"></i>
                      Phone & Email
                    </h5>
                    <ul className="list-unstyled">
                      <li className="mb-2">
                        <strong>General Inquiries:</strong> (*************
                      </li>
                      <li className="mb-2">
                        <strong>Job Seekers:</strong> (*************
                      </li>
                      <li className="mb-2">
                        <strong>Employers:</strong> (*************
                      </li>
                      <li className="mb-2">
                        <strong>Email:</strong> <EMAIL>
                      </li>
                      <li>
                        <strong>Emergency After Hours:</strong> (*************
                      </li>
                    </ul>
                  </div>

                  <div>
                    <h5 className="mb-3 d-flex align-items-center">
                      <i className="fa fa-clock-o text-primary me-3"></i>
                      Hours of Operation
                    </h5>
                    <ul className="list-unstyled">
                      <li className="mb-2">
                        <strong>Monday-Friday:</strong> 8:00 AM - 6:00 PM
                      </li>
                      <li className="mb-2">
                        <strong>Saturday:</strong> 9:00 AM - 2:00 PM
                      </li>
                      <li>
                        <strong>Sunday:</strong> Closed
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-0">
        <div className="container-fluid px-0">
          <div className="ratio ratio-21x9">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2969.************!2d-87.6312390845582!3d41.87811357922046!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNDHCsDUyJzQxLjIiTiA4N8KwMzcnNDAuNyJX!5e0!3m2!1sen!2sus!4v1620000000000!5m2!1sen!2sus"
              style={{ border: 0 }}
              allowFullScreen=""
              loading="lazy"
              title="Prime Staff Works Location"
            ></iframe>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-5 bg-primary text-white">
        <div className="container text-center py-4">
          <h2 className="fw-bold mb-4">Need Immediate Assistance?</h2>
          <p className="lead mb-5">
            Our team is available 24/7 for urgent staffing needs
          </p>
          <div className="d-flex flex-wrap justify-content-center gap-3">
            <a href="tel:5551234567" className="btn btn-light btn-lg px-4">
              <i className="fa fa-phone me-2"></i> Call Now: (*************
            </a>
            <a
              href="mailto:<EMAIL>"
              className="btn btn-outline-light btn-lg px-4"
            >
              <i className="fa fa-envelope me-2"></i> Email Us
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
