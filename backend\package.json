{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js"}, "keywords": [], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "colors": "^1.4.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^6.7.0", "helmet": "^7.0.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.0", "mongoose": "^7.4.1", "morgan": "^1.10.0", "nodemailer": "^7.0.3", "xss-clean": "^0.1.1"}, "devDependencies": {"nodemon": "^3.1.10"}}