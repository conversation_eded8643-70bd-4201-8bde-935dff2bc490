import React, { createContext, useReducer, useEffect } from "react";
import { authAPI } from "../utils/api";
import { toast } from "react-toastify";

// Initial state
const initialState = {
  token: localStorage.getItem("token"),
  isAuthenticated: false,
  loading: true,
  user: null,
  error: null,
};

// Action types
const AUTH_SUCCESS = "AUTH_SUCCESS";
const AUTH_ERROR = "AUTH_ERROR";
const LOGOUT = "LOGOUT";
const CLEAR_ERRORS = "CLEAR_ERRORS";
const SET_LOADING = "SET_LOADING";

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_SUCCESS:
      localStorage.setItem("token", action.payload.token);
      localStorage.setItem("user", JSON.stringify(action.payload.user));
      return {
        ...state,
        token: action.payload.token,
        user: action.payload.user,
        isAuthenticated: true,
        loading: false,
        error: null,
      };
    case AUTH_ERROR:
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      return {
        ...state,
        token: null,
        user: null,
        isAuthenticated: false,
        loading: false,
        error: action.payload,
      };
    case LOGOUT:
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      return {
        ...state,
        token: null,
        user: null,
        isAuthenticated: false,
        loading: false,
        error: null,
      };
    case CLEAR_ERRORS:
      return {
        ...state,
        error: null,
      };
    case SET_LOADING:
      return {
        ...state,
        loading: action.payload,
      };
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check if user is logged in on app load
  useEffect(() => {
    const token = localStorage.getItem("token");
    const user = localStorage.getItem("user");

    if (token && user) {
      try {
        const parsedUser = JSON.parse(user);
        dispatch({
          type: AUTH_SUCCESS,
          payload: { token, user: parsedUser },
        });
      } catch (error) {
        dispatch({ type: AUTH_ERROR, payload: "Invalid user data" });
      }
    } else {
      dispatch({ type: SET_LOADING, payload: false });
    }
  }, []);

  // Register user
  const register = async (userData) => {
    dispatch({ type: SET_LOADING, payload: true });
    try {
      const response = await authAPI.register(userData);
      dispatch({
        type: AUTH_SUCCESS,
        payload: response,
      });
      toast.success("Registration successful!");
      console.log("User registered: ", response);
      return response;
    } catch (error) {
      const errorMessage = error.response?.data?.error || "Registration failed";
      dispatch({
        type: AUTH_ERROR,
        payload: errorMessage,
      });
      toast.error(errorMessage);
      throw error;
    }
  };

  // Login user
  const login = async (credentials) => {
    dispatch({ type: SET_LOADING, payload: true });
    try {
      const response = await authAPI.login(credentials);
      dispatch({
        type: AUTH_SUCCESS,
        payload: response,
      });
      toast.success("Login successful!");
      return response;
    } catch (error) {
      const errorMessage = error.response?.data?.error || "Login failed";
      dispatch({
        type: AUTH_ERROR,
        payload: errorMessage,
      });
      toast.error(errorMessage);
      throw error;
    }
  };

  // Logout user
  const logout = () => {
    dispatch({ type: LOGOUT });
    toast.success("Logged out successfully");
  };

  // Clear errors
  const clearErrors = () => {
    dispatch({ type: CLEAR_ERRORS });
  };

  return (
    <AuthContext.Provider
      value={{
        token: state.token,
        isAuthenticated: state.isAuthenticated,
        loading: state.loading,
        user: state.user,
        error: state.error,
        register,
        login,
        logout,
        clearErrors,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
