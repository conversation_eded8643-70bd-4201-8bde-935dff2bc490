import React, { useState } from "react";
import JobCard from "../components/JobCard";
import jobs from "../data/data.json";

const Jobs = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState({
    location: "",
    jobType: "",
    experience: "",
  });

  const filteredJobs = jobs.filter((job) => {
    const matchesSearch =
      job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLocation = filters.location
      ? job.location.includes(filters.location)
      : true;
    const matchesType = filters.jobType ? job.type === filters.jobType : true;
    const matchesExperience = filters.experience
      ? (filters.experience === "entry" &&
          job.title.toLowerCase().includes("associate")) ||
        (filters.experience === "experienced" &&
          !job.title.toLowerCase().includes("associate"))
      : true;

    return matchesSearch && matchesLocation && matchesType && matchesExperience;
  });

  return (
    <div className="jobs-page">
      {/* Hero Section */}
      <section className="hero-section bg-primary text-white py-5">
        <div className="container py-5">
          <div className="row align-items-center">
            <div className="col-lg-6 mb-4 mb-lg-0">
              <h1 className="display-4 fw-bold mb-4">
                Warehouse Job Opportunities
              </h1>
              <p className="lead">
                Browse our current openings and find your next career move
              </p>
            </div>
            <div className="col-lg-6">
              <img
                src="/images/jobs-hero.jpg"
                alt="Happy warehouse worker"
                className="img-fluid rounded shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Job Search Section */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="card shadow-sm">
            <div className="card-body p-4">
              <div className="row g-3">
                <div className="col-md-6">
                  <label htmlFor="search" className="form-label">
                    Search Jobs
                  </label>
                  <div className="input-group">
                    <span className="input-group-text">
                      <i className="fa fa-search"></i>
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      id="search"
                      placeholder="Job title, keywords..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                <div className="col-md-2">
                  <label htmlFor="location" className="form-label">
                    Location
                  </label>
                  <select
                    className="form-select"
                    id="location"
                    value={filters.location}
                    onChange={(e) =>
                      setFilters({ ...filters, location: e.target.value })
                    }
                  >
                    <option value="">All Locations</option>
                    <option value="Chicago">Chicago</option>
                    <option value="New York">New York</option>
                    <option value="Los Angeles">Los Angeles</option>
                    <option value="Dallas">Dallas</option>
                    <option value="Atlanta">Atlanta</option>
                  </select>
                </div>
                <div className="col-md-2">
                  <label htmlFor="jobType" className="form-label">
                    Job Type
                  </label>
                  <select
                    className="form-select"
                    id="jobType"
                    value={filters.jobType}
                    onChange={(e) =>
                      setFilters({ ...filters, jobType: e.target.value })
                    }
                  >
                    <option value="">All Types</option>
                    <option value="Full-time">Full-time</option>
                    <option value="Part-time">Part-time</option>
                    <option value="Temporary">Temporary</option>
                    <option value="Contract">Contract</option>
                  </select>
                </div>
                <div className="col-md-2">
                  <label htmlFor="experience" className="form-label">
                    Experience
                  </label>
                  <select
                    className="form-select"
                    id="experience"
                    value={filters.experience}
                    onChange={(e) =>
                      setFilters({ ...filters, experience: e.target.value })
                    }
                  >
                    <option value="">All Levels</option>
                    <option value="entry">Entry Level</option>
                    <option value="experienced">Experienced</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Job Listings */}
      <section className="py-5">
        <div className="container">
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h2 className="fw-bold mb-0">
              {filteredJobs.length} Jobs Available
            </h2>
            <div className="d-flex align-items-center">
              <span className="me-2">Sort by:</span>
              <select className="form-select form-select-sm w-auto">
                <option>Most Recent</option>
                <option>Highest Pay</option>
                <option>Location</option>
              </select>
            </div>
          </div>

          {filteredJobs.length === 0 ? (
            <div className="text-center py-5">
              <i className="fa fa-folder-open fa-4x text-muted mb-4"></i>
              <h3>No jobs match your search criteria</h3>
              <p className="text-muted">
                Try adjusting your filters or search terms
              </p>
              <button
                className="btn btn-primary"
                onClick={() => {
                  setSearchTerm("");
                  setFilters({
                    location: "",
                    jobType: "",
                    experience: "",
                  });
                }}
              >
                Clear All Filters
              </button>
            </div>
          ) : (
            <div className="row g-4">
              {filteredJobs.map((job) => (
                <div key={job.id} className="col-12">
                  <JobCard job={job} />
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-5 bg-light d-none">
        <div className="container">
          <div className="text-center mb-5">
            <h2 className="fw-bold">
              Why Job Seekers Choose Prime Staff Works
            </h2>
            <p className="lead text-muted">
              Benefits of working with our agency
            </p>
          </div>
          <div className="row g-4">
            {[
              {
                icon: "fa-bolt",
                title: "Quick Placement",
                desc: "We fill positions fast, often placing workers within 24-48 hours",
              },
              {
                icon: "fa-money",
                title: "Competitive Pay",
                desc: "We negotiate the best wages and benefits for our candidates",
              },
              {
                icon: "fa-shield",
                title: "Job Security",
                desc: "We work with stable companies that offer long-term opportunities",
              },
              {
                icon: "fa-graduation-cap",
                title: "Training",
                desc: "Free skills training and certification programs available",
              },
              {
                icon: "fa-line-chart",
                title: "Career Growth",
                desc: "Many temp positions convert to permanent with growth potential",
              },
              {
                icon: "fa-heart",
                title: "Support",
                desc: "Dedicated reps to support you throughout your employment",
              },
            ].map((item, index) => (
              <div key={index} className="col-md-4">
                <div className="card h-100 border-0 shadow-sm hover-effect">
                  <div className="card-body p-4 text-center">
                    <i
                      className={`fa ${item.icon} fa-3x text-primary mb-3`}
                    ></i>
                    <h4 className="mb-3">{item.title}</h4>
                    <p className="text-muted mb-0">{item.desc}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-5 bg-primary text-white d-none">
        <div className="container text-center py-4">
          <h2 className="fw-bold mb-4">Can't Find What You're Looking For?</h2>
          <p className="lead mb-5">
            Join our talent network and we'll notify you when matching jobs
            become available.
          </p>
          <div className="d-flex flex-wrap justify-content-center gap-3">
            <a href="/register" className="btn btn-light btn-lg px-4">
              <i className="fa fa-user-plus me-2"></i> Join Our Network
            </a>
            <a href="/contact" className="btn btn-outline-light btn-lg px-4">
              <i className="fa fa-question-circle me-2"></i> Get Help
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Jobs;
