import { Link } from "react-router-dom";

import primeStaffWorksLogo from "../assets/images/prime-logo3.png";

const Footer = () => {
  return (
    <footer className="footer py-5 mt-5">
      <div className="container">
        <div className="row">
          <div className="col-lg-3 mb-4 mb-lg-0">
            <h5 className="mb-4">
              {/* <i className="fa fa-users me-2"></i> Prime Staff Works */}
              <img
                src={primeStaffWorksLogo}
                alt="prime staff works logo"
                className="img-fluid"
                style={{
                  maxHeight: "50px",
                  maxWidth: "100%",
                  objectFit: "contain",
                }}
              />
            </h5>
            <p className="text-muted">
              Fast Staff. Lasting Work. Connecting warehouse talent with leading
              employers.
            </p>
          </div>

          <div className="col-lg-3 mb-4 mb-lg-0">
            <h5 className="mb-4">Quick Links</h5>
            <ul className="list-unstyled">
              <li className="mb-2">
                <Link to="/" className="text-decoration-none text-muted">
                  <i className="fa fa-angle-right me-2"></i>Home
                </Link>
              </li>
              <li className="mb-2">
                <Link to="/about" className="text-decoration-none text-muted">
                  <i className="fa fa-angle-right me-2"></i>About Us
                </Link>
              </li>
              <li className="mb-2">
                <Link
                  to="/services"
                  className="text-decoration-none text-muted"
                >
                  <i className="fa fa-angle-right me-2"></i>Services
                </Link>
              </li>
              <li className="mb-2">
                <Link to="/jobs" className="text-decoration-none text-muted">
                  <i className="fa fa-angle-right me-2"></i>Job Listings
                </Link>
              </li>
              <li className="mb-2">
                <Link to="/contact" className="text-decoration-none text-muted">
                  <i className="fa fa-angle-right me-2"></i>Contact
                </Link>
              </li>
            </ul>
          </div>

          <div className="col-lg-3 mb-4 mb-lg-0">
            <h5 className="mb-4">Services</h5>
            <ul className="list-unstyled">
              <li className="mb-2 text-muted">
                <i className="fa fa-angle-right me-2"></i>Temporary Staffing
              </li>
              <li className="mb-2 text-muted">
                <i className="fa fa-angle-right me-2"></i>Long-Term Placements
              </li>
              <li className="mb-2 text-muted">
                <i className="fa fa-angle-right me-2"></i>Team Staffing
              </li>
              <li className="mb-2 text-muted">
                <i className="fa fa-angle-right me-2"></i>Payroll Services
              </li>
              <li className="mb-2 text-muted">
                <i className="fa fa-angle-right me-2"></i>On-Site Management
              </li>
            </ul>
          </div>

          <div className="col-lg-3">
            <h5 className="mb-4">Contact Us</h5>
            <address className="text-muted">
              <p>
                <i className="fa fa-map-marker me-2"></i>
                2910 Pleasant Ave
                <br />
                Minneapolis, MN 55408
              </p>
              <p>
                <i className="fa fa-phone me-2"></i>
                (*************
              </p>
              <p>
                <i className="fa fa-envelope me-2"></i>
                <EMAIL>
              </p>
            </address>
          </div>
        </div>

        <hr className="my-4" />

        <div className="row">
          <div className="col-md-6 text-center text-md-start">
            <p className="text-muted mb-0">
              &copy; {new Date().getFullYear()} Prime Staff Works. All rights
              reserved.
            </p>
          </div>
          <div className="col-md-6 text-center text-md-end">
            <div className="d-flex justify-content-md-end justify-content-center gap-3">
              <a
                href="https://www.facebook.com/primestaffworks/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted"
              >
                <i className="fa fa-facebook"></i>
              </a>
              <a href="#" className="text-muted">
                <i className="fa fa-twitter"></i>
              </a>
              <a href="#" className="text-muted">
                <i className="fa fa-linkedin"></i>
              </a>
              <a href="#" className="text-muted">
                <i className="fa fa-instagram"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
