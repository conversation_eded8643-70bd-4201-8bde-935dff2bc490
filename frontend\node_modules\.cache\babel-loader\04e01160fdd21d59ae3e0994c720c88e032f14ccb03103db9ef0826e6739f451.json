{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\primestaffworks\\\\frontend\\\\src\\\\pages\\\\About.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// import React from \"react\";\n// import TeamMember from \"../components/TeamMember\";\n\nconst About = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"about-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero-section  bg-primary text-white py-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container py-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row align-items-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-8 mb-4 mb-lg-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"display-3 fw-bold mb-4\",\n              children: \"About Prime Staff Works\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 12,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"lead\",\n              children: \"Delivering exceptional warehouse staffing solutions since 2020\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-6 mb-4 mb-lg-0\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              // src=\"/images/warehouse-team.jpg\"\n              src: \"https://plus.unsplash.com/premium_photo-1663090614415-a724fe8e2a92?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D\",\n              alt: \"Our team in action\",\n              className: \"img-fluid rounded shadow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"fw-bold mb-4\",\n              children: \"Our Story\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                children: \"Prime Staff Works\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), \" launched with a clear goal: to deliver fast, dependable staffing solutions tailored for the warehouse and logistics industry. What began as a small regional agency has grown into a trusted national partner, known for our commitment to speed, quality, and integrity.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\" \", \"Today, we serve hundreds of businesses across the country\\u2014matching them with thousands of reliable, skilled warehouse professionals each year. Our growth is driven by strong relationships, deep industry knowledge, and a passion for helping clients meet their workforce needs efficiently.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5 bg-light\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"fw-bold\",\n            children: \"Our Mission & Values\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"lead text-muted\",\n            children: \"The principles that guide everything we do\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row g-4\",\n          children: [{\n            icon: \"fa-bolt\",\n            title: \"Speed\",\n            desc: \"We respond quickly to staffing needs, often filling positions within 24 hours\"\n          }, {\n            icon: \"fa-thumbs-up\",\n            title: \"Quality\",\n            desc: \"Our rigorous screening ensures we provide only the most qualified candidates\"\n          }, {\n            icon: \"fa-handshake-o\",\n            title: \"Integrity\",\n            desc: \"We build relationships based on honesty, transparency and trust\"\n          }, {\n            icon: \"fa-line-chart\",\n            title: \"Results\",\n            desc: \"We measure success by our clients' satisfaction and workers' success\"\n          }, {\n            icon: \"fa-heart\",\n            title: \"Care\",\n            desc: \"We treat both clients and workers with respect and understanding\"\n          }, {\n            icon: \"fa-lightbulb-o\",\n            title: \"Innovation\",\n            desc: \"We continuously improve our processes to deliver better solutions\"\n          }].map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card h-100 border-0 shadow-sm hover-effect\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-body p-4 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: `fa ${item.icon} fa-3x text-primary mb-3`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 109,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"mb-3\",\n                  children: item.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted mb-0\",\n                  children: item.desc\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"fw-bold\",\n            children: \"Meet Our Leadership Team\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"lead text-muted\",\n            children: \"The experienced professionals guiding our company\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5 bg-primary text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container text-center py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"fw-bold mb-4\",\n          children: \"Join the Prime Staff Works Family\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"lead mb-5\",\n          children: \"Whether you're looking for talent or looking for work, we'd love to connect with you.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-wrap justify-content-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/contact\",\n            className: \"btn btn-light btn-lg px-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fa fa-phone me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), \" Contact Us\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/jobs\",\n            className: \"btn btn-outline-light btn-lg px-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fa fa-search me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), \" Browse Jobs\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["About", "_jsxDEV", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "icon", "title", "desc", "map", "item", "index", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/primestaffworks/frontend/src/pages/About.js"], "sourcesContent": ["// import React from \"react\";\r\n// import TeamMember from \"../components/TeamMember\";\r\n\r\nconst About = () => {\r\n  return (\r\n    <div className=\"about-page\">\r\n      {/* Hero Section */}\r\n      <section className=\"hero-section  bg-primary text-white py-5\">\r\n        <div className=\"container py-5\">\r\n          <div className=\"row align-items-center\">\r\n            <div className=\"col-lg-8 mb-4 mb-lg-0\">\r\n              <h1 className=\"display-3 fw-bold mb-4\">\r\n                About Prime Staff Works\r\n              </h1>\r\n              <p className=\"lead\">\r\n                Delivering exceptional warehouse staffing solutions since 2020\r\n              </p>\r\n            </div>\r\n            {/* <div className=\"col-lg-6\">\r\n              <img\r\n                src=\"/images/about-hero.jpg\"\r\n                alt=\"Prime Staff Works team\"\r\n                className=\"img-fluid rounded shadow-lg\"\r\n              />\r\n            </div> */}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Our Story */}\r\n      <section className=\"py-5\">\r\n        <div className=\"container\">\r\n          <div className=\"row align-items-center\">\r\n            <div className=\"col-lg-6 mb-4 mb-lg-0\">\r\n              <img\r\n                // src=\"/images/warehouse-team.jpg\"\r\n                src=\"https://plus.unsplash.com/premium_photo-1663090614415-a724fe8e2a92?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D\"\r\n                alt=\"Our team in action\"\r\n                className=\"img-fluid rounded shadow\"\r\n              />\r\n            </div>\r\n            <div className=\"col-lg-6\">\r\n              <h2 className=\"fw-bold mb-4\">Our Story</h2>\r\n              <p>\r\n                <i>Prime Staff Works</i> launched with a clear goal: to deliver\r\n                fast, dependable staffing solutions tailored for the warehouse\r\n                and logistics industry. What began as a small regional agency\r\n                has grown into a trusted national partner, known for our\r\n                commitment to speed, quality, and integrity.\r\n              </p>\r\n              <p>\r\n                {\" \"}\r\n                Today, we serve hundreds of businesses across the\r\n                country—matching them with thousands of reliable, skilled\r\n                warehouse professionals each year. Our growth is driven by\r\n                strong relationships, deep industry knowledge, and a passion for\r\n                helping clients meet their workforce needs efficiently.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Mission & Values */}\r\n      <section className=\"py-5 bg-light\">\r\n        <div className=\"container\">\r\n          <div className=\"text-center mb-5\">\r\n            <h1 className=\"fw-bold\">Our Mission & Values</h1>\r\n            <p className=\"lead text-muted\">\r\n              The principles that guide everything we do\r\n            </p>\r\n          </div>\r\n          <div className=\"row g-4\">\r\n            {[\r\n              {\r\n                icon: \"fa-bolt\",\r\n                title: \"Speed\",\r\n                desc: \"We respond quickly to staffing needs, often filling positions within 24 hours\",\r\n              },\r\n              {\r\n                icon: \"fa-thumbs-up\",\r\n                title: \"Quality\",\r\n                desc: \"Our rigorous screening ensures we provide only the most qualified candidates\",\r\n              },\r\n              {\r\n                icon: \"fa-handshake-o\",\r\n                title: \"Integrity\",\r\n                desc: \"We build relationships based on honesty, transparency and trust\",\r\n              },\r\n              {\r\n                icon: \"fa-line-chart\",\r\n                title: \"Results\",\r\n                desc: \"We measure success by our clients' satisfaction and workers' success\",\r\n              },\r\n              {\r\n                icon: \"fa-heart\",\r\n                title: \"Care\",\r\n                desc: \"We treat both clients and workers with respect and understanding\",\r\n              },\r\n              {\r\n                icon: \"fa-lightbulb-o\",\r\n                title: \"Innovation\",\r\n                desc: \"We continuously improve our processes to deliver better solutions\",\r\n              },\r\n            ].map((item, index) => (\r\n              <div key={index} className=\"col-md-4\">\r\n                <div className=\"card h-100 border-0 shadow-sm hover-effect\">\r\n                  <div className=\"card-body p-4 text-center\">\r\n                    <i\r\n                      className={`fa ${item.icon} fa-3x text-primary mb-3`}\r\n                    ></i>\r\n                    <h4 className=\"mb-3\">{item.title}</h4>\r\n                    <p className=\"text-muted mb-0\">{item.desc}</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Our Team */}\r\n      <section className=\"py-5\">\r\n        <div className=\"container\">\r\n          <div className=\"text-center mb-5\">\r\n            <h2 className=\"fw-bold\">Meet Our Leadership Team</h2>\r\n            <p className=\"lead text-muted\">\r\n              The experienced professionals guiding our company\r\n            </p>\r\n          </div>\r\n          {/* <div className=\"row g-4\">\r\n            <TeamMember\r\n              name=\"Robert Johnson\"\r\n              position=\"CEO & Founder\"\r\n              bio=\"With over 25 years in logistics and staffing, Robert founded Prime Staff Works to revolutionize warehouse staffing.\"\r\n              img=\"/images/team1.jpg\"\r\n            />\r\n            <TeamMember\r\n              name=\"Sarah Williams\"\r\n              position=\"COO\"\r\n              bio=\"Sarah oversees operations with a focus on efficiency and client satisfaction.\"\r\n              img=\"/images/team2.jpg\"\r\n            />\r\n            <TeamMember\r\n              name=\"Michael Chen\"\r\n              position=\"Director of Recruitment\"\r\n              bio=\"Michael leads our talent acquisition team, ensuring we find the best candidates.\"\r\n              img=\"/images/team3.jpg\"\r\n            />\r\n            <TeamMember\r\n              name=\"Emily Rodriguez\"\r\n              position=\"Client Relations\"\r\n              bio=\"Emily builds and maintains our strong relationships with client companies.\"\r\n              img=\"/images/team4.jpg\"\r\n            />\r\n          </div> */}\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-5 bg-primary text-white\">\r\n        <div className=\"container text-center py-4\">\r\n          <h2 className=\"fw-bold mb-4\">Join the Prime Staff Works Family</h2>\r\n          <p className=\"lead mb-5\">\r\n            Whether you're looking for talent or looking for work, we'd love to\r\n            connect with you.\r\n          </p>\r\n          <div className=\"d-flex flex-wrap justify-content-center gap-3\">\r\n            <a href=\"/contact\" className=\"btn btn-light btn-lg px-4\">\r\n              <i className=\"fa fa-phone me-2\"></i> Contact Us\r\n            </a>\r\n            <a href=\"/jobs\" className=\"btn btn-outline-light btn-lg px-4\">\r\n              <i className=\"fa fa-search me-2\"></i> Browse Jobs\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default About;\r\n"], "mappings": ";;AAAA;AACA;;AAEA,MAAMA,KAAK,GAAGA,CAAA,KAAM;EAClB,oBACEC,OAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAEzBF,OAAA;MAASC,SAAS,EAAC,0CAA0C;MAAAC,QAAA,eAC3DF,OAAA;QAAKC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BF,OAAA;UAAKC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,eACrCF,OAAA;YAAKC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCF,OAAA;cAAIC,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAEvC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLN,OAAA;cAAGC,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAEpB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVN,OAAA;MAASC,SAAS,EAAC,MAAM;MAAAC,QAAA,eACvBF,OAAA;QAAKC,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBF,OAAA;UAAKC,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCF,OAAA;YAAKC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpCF,OAAA;cACE;cACAO,GAAG,EAAC,8KAA8K;cAClLC,GAAG,EAAC,oBAAoB;cACxBP,SAAS,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNN,OAAA;YAAKC,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBF,OAAA;cAAIC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3CN,OAAA;cAAAE,QAAA,gBACEF,OAAA;gBAAAE,QAAA,EAAG;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,8QAK1B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJN,OAAA;cAAAE,QAAA,GACG,GAAG,EAAC,sSAMP;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVN,OAAA;MAASC,SAAS,EAAC,eAAe;MAAAC,QAAA,eAChCF,OAAA;QAAKC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBF,OAAA;UAAKC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BF,OAAA;YAAIC,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjDN,OAAA;YAAGC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAE/B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNN,OAAA;UAAKC,SAAS,EAAC,SAAS;UAAAC,QAAA,EACrB,CACC;YACEO,IAAI,EAAE,SAAS;YACfC,KAAK,EAAE,OAAO;YACdC,IAAI,EAAE;UACR,CAAC,EACD;YACEF,IAAI,EAAE,cAAc;YACpBC,KAAK,EAAE,SAAS;YAChBC,IAAI,EAAE;UACR,CAAC,EACD;YACEF,IAAI,EAAE,gBAAgB;YACtBC,KAAK,EAAE,WAAW;YAClBC,IAAI,EAAE;UACR,CAAC,EACD;YACEF,IAAI,EAAE,eAAe;YACrBC,KAAK,EAAE,SAAS;YAChBC,IAAI,EAAE;UACR,CAAC,EACD;YACEF,IAAI,EAAE,UAAU;YAChBC,KAAK,EAAE,MAAM;YACbC,IAAI,EAAE;UACR,CAAC,EACD;YACEF,IAAI,EAAE,gBAAgB;YACtBC,KAAK,EAAE,YAAY;YACnBC,IAAI,EAAE;UACR,CAAC,CACF,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChBd,OAAA;YAAiBC,SAAS,EAAC,UAAU;YAAAC,QAAA,eACnCF,OAAA;cAAKC,SAAS,EAAC,4CAA4C;cAAAC,QAAA,eACzDF,OAAA;gBAAKC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCF,OAAA;kBACEC,SAAS,EAAE,MAAMY,IAAI,CAACJ,IAAI;gBAA2B;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACLN,OAAA;kBAAIC,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAEW,IAAI,CAACH;gBAAK;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtCN,OAAA;kBAAGC,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAEW,IAAI,CAACF;gBAAI;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GATEQ,KAAK;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVN,OAAA;MAASC,SAAS,EAAC,MAAM;MAAAC,QAAA,eACvBF,OAAA;QAAKC,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBF,OAAA;UAAKC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BF,OAAA;YAAIC,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDN,OAAA;YAAGC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAE/B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2BH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVN,OAAA;MAASC,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eAC7CF,OAAA;QAAKC,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCF,OAAA;UAAIC,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnEN,OAAA;UAAGC,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAGzB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJN,OAAA;UAAKC,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5DF,OAAA;YAAGe,IAAI,EAAC,UAAU;YAACd,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACtDF,OAAA;cAAGC,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJN,OAAA;YAAGe,IAAI,EAAC,OAAO;YAACd,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAC3DF,OAAA;cAAGC,SAAS,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,gBACvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACU,EAAA,GAhLIjB,KAAK;AAkLX,eAAeA,KAAK;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}