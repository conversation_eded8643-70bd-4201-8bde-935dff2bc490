import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, Link, useLocation } from "react-router-dom";
import { toast } from "react-toastify";
import jobs from "../data/data.json";

const JobDetails = () => {
  const { id } = useParams();
  const { state } = useLocation();
  const [job, setJob] = useState(state?.job || null);
  const [loading, setLoading] = useState(!state?.job);
  const [application, setApplication] = useState({
    name: "",
    email: "",
    phone: "",
    resume: null,
    coverLetter: "",
  });

  useEffect(() => {
    if (!state?.job) {
      const fetchJobDetails = async () => {
        try {
          const foundJob = jobs.find((j) => j.id === parseInt(id));
          if (foundJob) {
            setJob(foundJob);
          } else {
            throw new Error("Job not found");
          }
        } catch (error) {
          toast.error(error.message);
        } finally {
          setLoading(false);
        }
      };

      fetchJobDetails();
    }
  }, [id, state]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setApplication((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleFileChange = (e) => {
    setApplication((prev) => ({
      ...prev,
      [e.target.name]: e.target.files[0],
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Application submitted:", { jobId: id, ...application });
    toast.success("Application submitted successfully!");
    setApplication({
      name: "",
      email: "",
      phone: "",
      resume: null,
      coverLetter: "",
    });
    // Close modal
    document.getElementById("closeModal").click();
  };

  if (loading) {
    return (
      <div className="container my-5 text-center">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <p className="mt-2">Loading job details...</p>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="container my-5">
        <div className="alert alert-danger">
          Job not found. <Link to="/jobs">Browse all jobs</Link>
        </div>
      </div>
    );
  }

  return (
    <div className="job-details-page">
      {/* Job Header */}
      <section className="bg-light py-5">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-md-8">
              <nav aria-label="breadcrumb">
                <ol className="breadcrumb">
                  <li className="breadcrumb-item">
                    <Link to="/">Home</Link>
                  </li>
                  <li className="breadcrumb-item">
                    <Link to="/jobs">Jobs</Link>
                  </li>
                  <li className="breadcrumb-item active" aria-current="page">
                    {job.title}
                  </li>
                </ol>
              </nav>
              <h1 className="display-6 fw-bold mb-3">{job.title}</h1>
              <div className="d-flex flex-wrap gap-3 mb-3">
                <span className="badge bg-primary">{job.type}</span>
                <span className="badge bg-secondary">{job.shift}</span>
                <span className="badge bg-success">{job.salary}</span>
              </div>
              <div className="d-flex flex-wrap gap-4">
                <span>
                  <i className="fa fa-building text-muted me-2"></i>
                  {job.company}
                </span>
                <span>
                  <i className="fa fa-map-marker text-muted me-2"></i>
                  {job.location}
                </span>
                <span>
                  <i className="fa fa-calendar text-muted me-2"></i>
                  Posted {job.posted}
                </span>
              </div>
            </div>
            <div className="col-md-4 text-md-end mt-4 mt-md-0">
              <Link to="/jobs" className="btn btn-outline-primary me-2">
                <i className="fa fa-arrow-left me-2"></i> Back to Jobs
              </Link>
              <button
                className="btn btn-primary"
                data-bs-toggle="modal"
                data-bs-target="#applyModal"
              >
                <i className="fa fa-paper-plane me-2"></i> Apply Now
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Job Content */}
      <section className="py-5">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 mb-5 mb-lg-0">
              <div className="card border-0 shadow-sm mb-4">
                <div className="card-body">
                  <h3 className="h4 mb-4">Job Description</h3>
                  <p>{job.description}</p>
                </div>
              </div>

              <div className="card border-0 shadow-sm mb-4">
                <div className="card-body">
                  <h3 className="h4 mb-4">Responsibilities</h3>
                  <ul className="list-unstyled">
                    {/* {job.responsibilities.map((item, index) => (
                      <li key={index} className="mb-2 d-flex">
                        <i className="fa fa-check-circle text-primary me-2 mt-1"></i>
                        <span>{item}</span>
                      </li>
                    ))} */}
                  </ul>
                </div>
              </div>

              <div className="card border-0 shadow-sm">
                <div className="card-body">
                  <h3 className="h4 mb-4">Requirements</h3>
                  <ul className="list-unstyled">
                    {/* {job.requirements.map((item, index) => (
                      <li key={index} className="mb-2 d-flex">
                        <i className="fa fa-check-circle text-primary me-2 mt-1"></i>
                        <span>{item}</span>
                      </li>
                    ))} */}
                  </ul>
                </div>
              </div>
            </div>

            <div className="col-lg-4">
              <div className="card border-0 shadow-sm mb-4">
                <div className="card-body">
                  <h3 className="h4 mb-4">Job Overview</h3>
                  <ul className="list-unstyled">
                    <li className="mb-3 d-flex">
                      <i className="fa fa-briefcase text-primary me-3 mt-1"></i>
                      <div>
                        <h6 className="mb-1">Job Type</h6>
                        <p className="mb-0 text-muted">{job.type}</p>
                      </div>
                    </li>
                    <li className="mb-3 d-flex">
                      <i className="fa fa-clock-o text-primary me-3 mt-1"></i>
                      <div>
                        <h6 className="mb-1">Shift</h6>
                        <p className="mb-0 text-muted">{job.shift}</p>
                      </div>
                    </li>
                    <li className="mb-3 d-flex">
                      <i className="fa fa-money text-primary me-3 mt-1"></i>
                      <div>
                        <h6 className="mb-1">Salary</h6>
                        <p className="mb-0 text-muted">{job.salary}</p>
                      </div>
                    </li>
                    <li className="mb-3 d-flex">
                      <i className="fa fa-map-marker text-primary me-3 mt-1"></i>
                      <div>
                        <h6 className="mb-1">Location</h6>
                        <p className="mb-0 text-muted">{job.address}</p>
                      </div>
                    </li>
                    <li className="d-flex">
                      <i className="fa fa-calendar text-primary me-3 mt-1"></i>
                      <div>
                        <h6 className="mb-1">Date Posted</h6>
                        <p className="mb-0 text-muted">{job.posted}</p>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>

              <div className="card border-0 shadow-sm">
                <div className="card-body">
                  <h3 className="h4 mb-4">Benefits</h3>
                  <ul className="list-unstyled">
                    {/* {job.benefits.map((item, index) => (
                      <li key={index} className="mb-2 d-flex">
                        <i className="fa fa-check-circle text-primary me-2 mt-1"></i>
                        <span>{item}</span>
                      </li>
                    ))} */}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Company Info */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="card border-0 shadow-sm">
            <div className="card-body p-4">
              <h3 className="h4 mb-4">About {job.company}</h3>
              <p>{job.companyDescription}</p>
              <div className="d-flex flex-wrap gap-3 mt-4">
                <button className="btn btn-outline-primary">
                  <i className="fa fa-globe me-2"></i> Visit Website
                </button>
                <button className="btn btn-outline-secondary">
                  <i className="fa fa-building me-2"></i> All Jobs
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Similar Jobs */}
      <section className="py-5">
        <div className="container">
          <h3 className="h4 mb-4">Similar Jobs</h3>
          <div className="row">
            {jobs
              .filter((j) => j.id !== parseInt(id) && j.type === job.type)
              .slice(0, 3)
              .map((similarJob) => (
                <div key={similarJob.id} className="col-md-6 col-lg-4 mb-4">
                  <div className="card h-100 border-0 shadow-sm hover-effect">
                    <div className="card-body">
                      <h4 className="h5 mb-2">{similarJob.title}</h4>
                      <p className="text-muted small mb-3">
                        {similarJob.company} • {similarJob.location}
                      </p>
                      <div className="d-flex flex-wrap gap-2 mb-3">
                        <span className="badge bg-primary">
                          {similarJob.type}
                        </span>
                        <span className="badge bg-secondary">
                          {similarJob.shift}
                        </span>
                        <span className="badge bg-success">
                          {similarJob.salary}
                        </span>
                      </div>
                      <p className="small text-muted mb-3">
                        {similarJob.description.substring(0, 100)}...
                      </p>
                      <Link
                        to={`/jobs/${similarJob.id}`}
                        className="btn btn-sm btn-outline-primary"
                        onClick={() => window.scrollTo(0, 0)}
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </section>

      {/* Application Modal */}
      <div
        className="modal fade"
        id="applyModal"
        tabIndex="-1"
        aria-labelledby="applyModalLabel"
        aria-hidden="true"
      >
        <div className="modal-dialog modal-lg">
          <div className="modal-content">
            <div className="modal-header bg-primary text-white">
              <h5 className="modal-title" id="applyModalLabel">
                Apply for {job.title}
              </h5>
              <button
                id="closeModal"
                type="button"
                className="btn-close btn-close-white"
                data-bs-dismiss="modal"
                aria-label="Close"
              ></button>
            </div>
            <form onSubmit={handleSubmit}>
              <div className="modal-body">
                <div className="mb-3">
                  <label htmlFor="name" className="form-label">
                    Full Name
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    id="name"
                    name="name"
                    value={application.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label htmlFor="email" className="form-label">
                      Email Address
                    </label>
                    <input
                      type="email"
                      className="form-control"
                      id="email"
                      name="email"
                      value={application.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div className="col-md-6 mb-3">
                    <label htmlFor="phone" className="form-label">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      className="form-control"
                      id="phone"
                      name="phone"
                      value={application.phone}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
                <div className="mb-3">
                  <label htmlFor="resume" className="form-label">
                    Resume
                  </label>
                  <input
                    type="file"
                    className="form-control"
                    id="resume"
                    name="resume"
                    onChange={handleFileChange}
                    accept=".pdf,.doc,.docx"
                    required
                  />
                  <div className="form-text">PDF, DOC or DOCX files only</div>
                </div>
                <div className="mb-3">
                  <label htmlFor="coverLetter" className="form-label">
                    Cover Letter (Optional)
                  </label>
                  <textarea
                    className="form-control"
                    id="coverLetter"
                    name="coverLetter"
                    rows="4"
                    value={application.coverLetter}
                    onChange={handleInputChange}
                  ></textarea>
                </div>
                <div className="form-check mb-3">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    id="agreeTerms"
                    required
                  />
                  <label className="form-check-label" htmlFor="agreeTerms">
                    I agree to the Terms and Conditions
                  </label>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-outline-secondary"
                  data-bs-dismiss="modal"
                >
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  <i className="fa fa-paper-plane me-2"></i> Submit Application
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobDetails;
