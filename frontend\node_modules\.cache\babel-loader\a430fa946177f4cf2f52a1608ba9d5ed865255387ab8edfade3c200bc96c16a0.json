{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\primestaffworks\\\\frontend\\\\src\\\\utils\\\\NotFound.js\";\nimport React from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotFound = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"notfound bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex align-items-center justify-content-center vh-100\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"display-1 fw-bold\",\n          children: \"404\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"fs-3\",\n          children: [\" \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-danger\",\n            children: \"SORRY\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), \", WE COULDN\\u2019T FIND THAT PAGE\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"lead\",\n          children: \"The page you\\u2019re looking for doesn\\u2019t exist.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"btn btn-primary\",\n          children: \"Go Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = NotFound;\nexport default NotFound;\nvar _c;\n$RefreshReg$(_c, \"NotFound\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "NotFound", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/primestaffworks/frontend/src/utils/NotFound.js"], "sourcesContent": ["import React from \"react\";\r\nimport { Link } from \"react-router-dom\";\r\n\r\nconst NotFound = () => {\r\n  return (\r\n    <div className=\"notfound bg-white\">\r\n      <div className=\"d-flex align-items-center justify-content-center vh-100\">\r\n        <div className=\"text-center\">\r\n          <h1 className=\"display-1 fw-bold\">404</h1>\r\n          <p className=\"fs-3\">\r\n            {\" \"}\r\n            <span className=\"text-danger\">SORRY</span>, WE COULDN’T FIND THAT\r\n            PAGE\r\n          </p>\r\n          <p className=\"lead\">The page you’re looking for doesn’t exist.</p>\r\n          <Link to=\"/\" className=\"btn btn-primary\">\r\n            Go Home\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotFound;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACED,OAAA;IAAKE,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChCH,OAAA;MAAKE,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACtEH,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BH,OAAA;UAAIE,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1CP,OAAA;UAAGE,SAAS,EAAC,MAAM;UAAAC,QAAA,GAChB,GAAG,eACJH,OAAA;YAAME,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,qCAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA;UAAGE,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAA0C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAClEP,OAAA,CAACF,IAAI;UAACU,EAAE,EAAC,GAAG;UAACN,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAEzC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GAnBIR,QAAQ;AAqBd,eAAeA,QAAQ;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}