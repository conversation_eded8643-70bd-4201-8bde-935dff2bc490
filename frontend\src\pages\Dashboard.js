import React, { useState, useEffect, useContext } from "react";
import { Link } from "react-router-dom";
// import AuthContext from "../context/authContext";

const Dashboard = () => {
  // const { user } = useContext(AuthContext);
  const [activeTab, setActiveTab] = useState("overview");
  const [stats, setStats] = useState(null);
  const [recentApplications, setRecentApplications] = useState([]);
  const [upcomingInterviews, setUpcomingInterviews] = useState([]);
  const [savedJobs, setSavedJobs] = useState([]);

  const user = {
    role: "admin",
  };

  useEffect(() => {
    // In a real app, this would fetch dashboard data from your API
    const fetchDashboardData = async () => {
      try {
        // Mock data based on user role
        if (user.role === "applicant") {
          setStats({
            applications: 12,
            interviews: 3,
            offers: 1,
            profileStrength: 85,
          });

          setRecentApplications([
            {
              id: 1,
              jobTitle: "Warehouse Associate",
              company: "ACME Distributors",
              status: "Under Review",
              date: "2023-05-15",
              location: "Chicago, IL",
            },
            {
              id: 2,
              jobTitle: "Forklift Operator",
              company: "Global Logistics",
              status: "Interview Scheduled",
              date: "2023-05-10",
              location: "Chicago, IL",
            },
          ]);

          setUpcomingInterviews([
            {
              id: 1,
              jobTitle: "Forklift Operator",
              company: "Global Logistics",
              date: "2023-05-20",
              time: "10:00 AM",
              type: "On-site",
              location: "123 Industrial Park, Chicago",
            },
          ]);

          setSavedJobs([
            {
              id: 3,
              title: "Inventory Specialist",
              company: "Retail Distribution Inc.",
              location: "Chicago, IL",
              salary: "$17-$19/hr",
              savedDate: "2023-05-01",
            },
          ]);
        } else if (user.role === "employer") {
          setStats({
            openPositions: 8,
            applications: 42,
            interviews: 15,
            hires: 3,
          });

          setRecentApplications([
            {
              id: 101,
              applicant: "John Smith",
              position: "Warehouse Associate",
              status: "New",
              date: "2023-05-16",
              experience: "2 years",
            },
            {
              id: 102,
              applicant: "Maria Garcia",
              position: "Forklift Operator",
              status: "Interview Scheduled",
              date: "2023-05-14",
              experience: "Certified",
            },
          ]);
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      }
    };

    fetchDashboardData();
  }, [user.role]);

  const renderApplicantDashboard = () => (
    <div className="row">
      {/* Stats Cards */}
      <div className="col-md-3 mb-4">
        <div className="card border-0 shadow-sm h-100">
          <div className="card-body">
            <h6 className="text-muted mb-3">Applications</h6>
            <h2 className="mb-0">{stats?.applications || 0}</h2>
          </div>
        </div>
      </div>
      <div className="col-md-3 mb-4">
        <div className="card border-0 shadow-sm h-100">
          <div className="card-body">
            <h6 className="text-muted mb-3">Interviews</h6>
            <h2 className="mb-0">{stats?.interviews || 0}</h2>
          </div>
        </div>
      </div>
      <div className="col-md-3 mb-4">
        <div className="card border-0 shadow-sm h-100">
          <div className="card-body">
            <h6 className="text-muted mb-3">Offers</h6>
            <h2 className="mb-0">{stats?.offers || 0}</h2>
          </div>
        </div>
      </div>
      <div className="col-md-3 mb-4">
        <div className="card border-0 shadow-sm h-100">
          <div className="card-body">
            <h6 className="text-muted mb-3">Profile Strength</h6>
            <div className="d-flex align-items-center">
              <div className="progress w-100 me-3" style={{ height: "8px" }}>
                <div
                  className="progress-bar bg-success"
                  role="progressbar"
                  style={{ width: `${stats?.profileStrength || 0}%` }}
                ></div>
              </div>
              <span>{stats?.profileStrength || 0}%</span>
            </div>
            <Link to="/profile" className="btn btn-link p-0 mt-2 small">
              Complete Profile
            </Link>
          </div>
        </div>
      </div>

      {/* Recent Applications */}
      <div className="col-lg-8 mb-4">
        <div className="card border-0 shadow-sm h-100">
          <div className="card-body">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <h5 className="mb-0">Recent Applications</h5>
              <Link
                to="/applications"
                className="btn btn-sm btn-outline-primary"
              >
                View All
              </Link>
            </div>
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead>
                  <tr>
                    <th>Position</th>
                    <th>Company</th>
                    <th>Status</th>
                    <th>Date</th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {recentApplications.map((app) => (
                    <tr key={app.id}>
                      <td>{app.jobTitle}</td>
                      <td>{app.company}</td>
                      <td>
                        <span
                          className={`badge ${
                            app.status === "Under Review"
                              ? "bg-info"
                              : app.status === "Interview Scheduled"
                              ? "bg-warning text-dark"
                              : "bg-success"
                          }`}
                        >
                          {app.status}
                        </span>
                      </td>
                      <td>{app.date}</td>
                      <td className="text-end">
                        <Link
                          to={`/applications/${app.id}`}
                          className="btn btn-sm btn-outline-secondary"
                        >
                          Details
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Upcoming Interviews */}
      <div className="col-lg-4 mb-4">
        <div className="card border-0 shadow-sm h-100">
          <div className="card-body">
            <h5 className="mb-3">Upcoming Interviews</h5>
            {upcomingInterviews.length > 0 ? (
              upcomingInterviews.map((interview) => (
                <div key={interview.id} className="mb-3 pb-3 border-bottom">
                  <div className="d-flex justify-content-between">
                    <h6 className="mb-1">{interview.jobTitle}</h6>
                    <span className="badge bg-light text-dark">
                      {interview.type}
                    </span>
                  </div>
                  <p className="small text-muted mb-1">{interview.company}</p>
                  <p className="small mb-1">
                    <i className="fa fa-calendar text-primary me-2"></i>
                    {interview.date} at {interview.time}
                  </p>
                  <p className="small mb-0">
                    <i className="fa fa-map-marker text-primary me-2"></i>
                    {interview.location}
                  </p>
                  <div className="d-flex gap-2 mt-2">
                    <button className="btn btn-sm btn-outline-primary">
                      <i className="fa fa-calendar me-1"></i> Add to Calendar
                    </button>
                    <button className="btn btn-sm btn-outline-secondary">
                      <i className="fa fa-directions me-1"></i> Directions
                    </button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-3">
                <i className="fa fa-calendar fa-2x text-muted mb-2"></i>
                <p className="text-muted">No upcoming interviews</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Saved Jobs */}
      <div className="col-12 mb-4">
        <div className="card border-0 shadow-sm">
          <div className="card-body">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <h5 className="mb-0">Saved Jobs</h5>
              <Link to="/jobs/saved" className="btn btn-sm btn-outline-primary">
                View All
              </Link>
            </div>
            <div className="row">
              {savedJobs.length > 0 ? (
                savedJobs.map((job) => (
                  <div key={job.id} className="col-md-4 mb-3">
                    <div className="card h-100 border-0 shadow-sm hover-effect">
                      <div className="card-body">
                        <h6 className="mb-1">{job.title}</h6>
                        <p className="small text-muted mb-2">
                          {job.company} • {job.location}
                        </p>
                        <p className="small mb-2">
                          <i className="fa fa-money text-primary me-1"></i>
                          {job.salary}
                        </p>
                        <div className="d-flex justify-content-between align-items-center">
                          <span className="small text-muted">
                            Saved on {job.savedDate}
                          </span>
                          <div>
                            <button className="btn btn-sm btn-outline-danger me-1">
                              <i className="fa fa-trash"></i>
                            </button>
                            <Link
                              to={`/jobs/${job.id}`}
                              className="btn btn-sm btn-primary"
                            >
                              Apply
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="col-12 text-center py-4">
                  <i className="fa fa-bookmark fa-2x text-muted mb-2"></i>
                  <p className="text-muted">No saved jobs</p>
                  <Link to="/jobs" className="btn btn-primary">
                    Browse Jobs
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderEmployerDashboard = () => (
    <div className="row">
      {/* Stats Cards */}
      <div className="col-md-3 mb-4">
        <div className="card border-0 shadow-sm h-100">
          <div className="card-body">
            <h6 className="text-muted mb-3">Open Positions</h6>
            <h2 className="mb-0">{stats?.openPositions || 0}</h2>
            <Link to="/jobs/post" className="btn btn-link p-0 mt-2 small">
              Post New Job
            </Link>
          </div>
        </div>
      </div>
      <div className="col-md-3 mb-4">
        <div className="card border-0 shadow-sm h-100">
          <div className="card-body">
            <h6 className="text-muted mb-3">Applications</h6>
            <h2 className="mb-0">{stats?.applications || 0}</h2>
            <Link to="/applications" className="btn btn-link p-0 mt-2 small">
              View All
            </Link>
          </div>
        </div>
      </div>
      <div className="col-md-3 mb-4">
        <div className="card border-0 shadow-sm h-100">
          <div className="card-body">
            <h6 className="text-muted mb-3">Interviews</h6>
            <h2 className="mb-0">{stats?.interviews || 0}</h2>
            <Link to="/interviews" className="btn btn-link p-0 mt-2 small">
              Schedule
            </Link>
          </div>
        </div>
      </div>
      <div className="col-md-3 mb-4">
        <div className="card border-0 shadow-sm h-100">
          <div className="card-body">
            <h6 className="text-muted mb-3">Recent Hires</h6>
            <h2 className="mb-0">{stats?.hires || 0}</h2>
            <Link to="/employees" className="btn btn-link p-0 mt-2 small">
              Manage
            </Link>
          </div>
        </div>
      </div>

      {/* Recent Applications */}
      <div className="col-lg-8 mb-4">
        <div className="card border-0 shadow-sm h-100">
          <div className="card-body">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <h5 className="mb-0">Recent Applications</h5>
              <Link
                to="/applications"
                className="btn btn-sm btn-outline-primary"
              >
                View All
              </Link>
            </div>
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead>
                  <tr>
                    <th>Applicant</th>
                    <th>Position</th>
                    <th>Experience</th>
                    <th>Status</th>
                    <th>Date</th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {recentApplications.map((app) => (
                    <tr key={app.id}>
                      <td>{app.applicant}</td>
                      <td>{app.position}</td>
                      <td>{app.experience}</td>
                      <td>
                        <span
                          className={`badge ${
                            app.status === "New"
                              ? "bg-info"
                              : app.status === "Interview Scheduled"
                              ? "bg-warning text-dark"
                              : "bg-success"
                          }`}
                        >
                          {app.status}
                        </span>
                      </td>
                      <td>{app.date}</td>
                      <td className="text-end">
                        <Link
                          to={`/applications/${app.id}`}
                          className="btn btn-sm btn-outline-secondary"
                        >
                          Review
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="col-lg-4 mb-4">
        <div className="card border-0 shadow-sm h-100">
          <div className="card-body">
            <h5 className="mb-3">Quick Actions</h5>
            <div className="d-grid gap-2">
              <Link to="/jobs/post" className="btn btn-primary">
                <i className="fa fa-plus me-2"></i> Post New Job
              </Link>
              <Link to="/applications" className="btn btn-outline-primary">
                <i className="fa fa-list me-2"></i> View Applications
              </Link>
              <Link to="/interviews" className="btn btn-outline-primary">
                <i className="fa fa-calendar me-2"></i> Schedule Interview
              </Link>
              <Link to="/employees" className="btn btn-outline-primary">
                <i className="fa fa-users me-2"></i> Manage Employees
              </Link>
            </div>

            <h5 className="mt-4 mb-3">Recent Activity</h5>
            <div className="list-group list-group-flush">
              <div className="list-group-item border-0 px-0 py-2">
                <div className="d-flex">
                  <div className="flex-shrink-0 text-primary">
                    <i className="fa fa-file-text"></i>
                  </div>
                  <div className="flex-grow-1 ms-3">
                    <p className="mb-0 small">
                      New application for Warehouse Supervisor
                    </p>
                    <p className="small text-muted mb-0">2 hours ago</p>
                  </div>
                </div>
              </div>
              <div className="list-group-item border-0 px-0 py-2">
                <div className="d-flex">
                  <div className="flex-shrink-0 text-primary">
                    <i className="fa fa-calendar-check-o"></i>
                  </div>
                  <div className="flex-grow-1 ms-3">
                    <p className="mb-0 small">
                      Interview scheduled for Forklift Operator
                    </p>
                    <p className="small text-muted mb-0">1 day ago</p>
                  </div>
                </div>
              </div>
              <div className="list-group-item border-0 px-0 py-2">
                <div className="d-flex">
                  <div className="flex-shrink-0 text-primary">
                    <i className="fa fa-thumbs-up"></i>
                  </div>
                  <div className="flex-grow-1 ms-3">
                    <p className="mb-0 small">
                      You hired John Smith for Warehouse Associate
                    </p>
                    <p className="small text-muted mb-0">3 days ago</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="dashboard-page mt-4">
      <div className="container py-5">
        {/* Dashboard Header */}
        <div className="row mb-4">
          <div className="col">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h1 className="h3 mb-1">Dashboard</h1>
                <p className="mb-0 text-muted">
                  Welcome back, {user?.name || "User"}!
                </p>
              </div>
              <div>
                <Link
                  to={user.role === "applicant" ? "/profile" : "/company"}
                  className="btn btn-outline-primary"
                >
                  <i className="fa fa-user me-2"></i>
                  {user.role === "applicant" ? "My Profile" : "Company Profile"}
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Dashboard Tabs */}
        <ul className="nav nav-tabs mb-4">
          <li className="nav-item">
            <button
              className={`nav-link ${activeTab === "overview" ? "active" : ""}`}
              onClick={() => setActiveTab("overview")}
            >
              <i className="fa fa-home me-2"></i> Overview
            </button>
          </li>
          <li className="nav-item">
            <button
              className={`nav-link ${activeTab === "jobs" ? "active" : ""}`}
              onClick={() => setActiveTab("jobs")}
            >
              <i className="fa fa-briefcase me-2"></i> Jobs
            </button>
          </li>
          <li className="nav-item">
            <button
              className={`nav-link ${
                activeTab === "applications" ? "active" : ""
              }`}
              onClick={() => setActiveTab("applications")}
            >
              <i className="fa fa-file-text me-2"></i> Applications
            </button>
          </li>
          {user.role === "employer" && (
            <li className="nav-item">
              <button
                className={`nav-link ${
                  activeTab === "employees" ? "active" : ""
                }`}
                onClick={() => setActiveTab("employees")}
              >
                <i className="fa fa-users me-2"></i> Employees
              </button>
            </li>
          )}
        </ul>

        {/* Dashboard Content */}
        {activeTab === "overview" && (
          <>
            {user.role === "applicant"
              ? renderApplicantDashboard()
              : renderEmployerDashboard()}
          </>
        )}

        {activeTab === "jobs" && (
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <h5 className="mb-0">
                {user.role === "applicant"
                  ? "Your Job Applications"
                  : "Your Job Postings"}
              </h5>
              <div className="text-center py-5">
                <i className="fa fa-briefcase fa-3x text-muted mb-3"></i>
                <p className="text-muted">
                  {user.role === "applicant"
                    ? "You haven't applied to any jobs yet."
                    : "You haven't posted any jobs yet."}
                </p>
                <Link
                  to={user.role === "applicant" ? "/jobs" : "/jobs/post"}
                  className="btn btn-primary"
                >
                  {user.role === "applicant" ? "Browse Jobs" : "Post a Job"}
                </Link>
              </div>
            </div>
          </div>
        )}

        {activeTab === "applications" && (
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <h5 className="mb-0">
                {user.role === "applicant"
                  ? "Your Applications"
                  : "Recent Applications"}
              </h5>
              <div className="text-center py-5">
                <i className="fa fa-file-text fa-3x text-muted mb-3"></i>
                <p className="text-muted">
                  {user.role === "applicant"
                    ? "You haven't submitted any applications yet."
                    : "No applications received yet."}
                </p>
              </div>
            </div>
          </div>
        )}

        {activeTab === "employees" && user.role === "employer" && (
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <h5 className="mb-0">Your Employees</h5>
              <div className="text-center py-5">
                <i className="fa fa-users fa-3x text-muted mb-3"></i>
                <p className="text-muted">
                  You haven\'t hired any employees yet.
                </p>
                <Link to="/applications" className="btn btn-primary">
                  View Candidates
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
