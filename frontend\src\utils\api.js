import axios from "axios";

// Create axios instance
const API = axios.create({
  // baseURL: process.env.REACT_APP_API_URL || "http://localhost:5000/api/v1",
  baseURL:
    process.env.REACT_APP_API_URL ||
    "https://primestaffworks-com.onrender.com/api/v1",
  withCredentials: true, // Include cookies in requests
});

// Request interceptor to add token to headers
API.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
API.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

// Auth API calls
export const authAPI = {
  // Register user
  register: async (userData) => {
    const response = await API.post("/auth/register", userData);
    return response.data;
  },

  // Login user
  login: async (credentials) => {
    const response = await API.post("/auth/login", credentials);
    return response.data;
  },

  // Get current user (for future use)
  getMe: async () => {
    const response = await API.get("/auth/me");
    return response.data;
  },

  // Logout user (for future use)
  logout: async () => {
    const response = await API.get("/auth/logout");
    return response.data;
  },
};

export default API;
