[{"C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\Navbar.js": "4", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Home.js": "5", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\About.js": "6", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\Footer.js": "7", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Login.js": "8", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Jobs.js": "9", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\JobDetails.js": "10", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Contact.js": "11", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Register.js": "12", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Dashboard.js": "13", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Services.js": "14", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\JobCard.js": "15", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\PrivateRoute.js": "16", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\context\\authContext.js": "17", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\utils\\api.js": "18", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\utils\\NotFound.js": "19"}, {"size": 552, "mtime": 1751051020649, "results": "20", "hashOfConfig": "21"}, {"size": 2821, "mtime": 1752178351268, "results": "22", "hashOfConfig": "21"}, {"size": 375, "mtime": 1751051019649, "results": "23", "hashOfConfig": "21"}, {"size": 3197, "mtime": 1752021079149, "results": "24", "hashOfConfig": "21"}, {"size": 9864, "mtime": 1751053073912, "results": "25", "hashOfConfig": "21"}, {"size": 7266, "mtime": 1751314546534, "results": "26", "hashOfConfig": "21"}, {"size": 5176, "mtime": 1751415325227, "results": "27", "hashOfConfig": "21"}, {"size": 5051, "mtime": 1752021027095, "results": "28", "hashOfConfig": "21"}, {"size": 10712, "mtime": 1751314248278, "results": "29", "hashOfConfig": "21"}, {"size": 56482, "mtime": 1751051020649, "results": "30", "hashOfConfig": "21"}, {"size": 53350, "mtime": 1751051020649, "results": "31", "hashOfConfig": "21"}, {"size": 7823, "mtime": 1752020930461, "results": "32", "hashOfConfig": "21"}, {"size": 65213, "mtime": 1751051020649, "results": "33", "hashOfConfig": "21"}, {"size": 7993, "mtime": 1751313911227, "results": "34", "hashOfConfig": "21"}, {"size": 5404, "mtime": 1751051019649, "results": "35", "hashOfConfig": "21"}, {"size": 638, "mtime": 1751051019649, "results": "36", "hashOfConfig": "21"}, {"size": 45457, "mtime": 1752104090420, "results": "37", "hashOfConfig": "21"}, {"size": 1627, "mtime": 1752103132560, "results": "38", "hashOfConfig": "21"}, {"size": 746, "mtime": 1752178324449, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "x0mttm", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\Navbar.js", ["97", "98"], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\Footer.js", ["99", "100", "101"], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Jobs.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\JobDetails.js", [], ["102", "103", "104", "105", "106", "107", "108", "109", "110", "111", "112", "113", "114"], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Contact.js", [], ["115", "116", "117", "118", "119", "120", "121", "122", "123", "124", "125", "126", "127"], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Register.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Dashboard.js", ["128"], ["129", "130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141"], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Services.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\JobCard.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\PrivateRoute.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\context\\authContext.js", [], ["142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154"], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\utils\\api.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\utils\\NotFound.js", [], [], {"ruleId": "155", "severity": 1, "message": "156", "line": 1, "column": 29, "nodeType": "157", "messageId": "158", "endLine": 1, "endColumn": 37}, {"ruleId": "155", "severity": 1, "message": "159", "line": 9, "column": 28, "nodeType": "157", "messageId": "158", "endLine": 9, "endColumn": 32}, {"ruleId": "160", "severity": 1, "message": "161", "line": 125, "column": 15, "nodeType": "162", "endLine": 125, "endColumn": 50}, {"ruleId": "160", "severity": 1, "message": "161", "line": 128, "column": 15, "nodeType": "162", "endLine": 128, "endColumn": 50}, {"ruleId": "160", "severity": 1, "message": "161", "line": 131, "column": 15, "nodeType": "162", "endLine": 131, "endColumn": 50}, {"ruleId": "163", "severity": 1, "message": "164", "line": 417, "column": 100, "nodeType": "157", "messageId": "165", "endLine": 417, "endColumn": 104, "suppressions": "166"}, {"ruleId": "163", "severity": 1, "message": "164", "line": 417, "column": 141, "nodeType": "157", "messageId": "165", "endLine": 417, "endColumn": 145, "suppressions": "167"}, {"ruleId": "155", "severity": 1, "message": "168", "line": 417, "column": 40460, "nodeType": "157", "messageId": "158", "endLine": 417, "endColumn": 40465, "suppressions": "169"}, {"ruleId": "155", "severity": 1, "message": "170", "line": 417, "column": 40594, "nodeType": "157", "messageId": "158", "endLine": 417, "endColumn": 40599, "suppressions": "171"}, {"ruleId": "155", "severity": 1, "message": "172", "line": 417, "column": 40728, "nodeType": "157", "messageId": "158", "endLine": 417, "endColumn": 40733, "suppressions": "173"}, {"ruleId": "155", "severity": 1, "message": "174", "line": 417, "column": 40838, "nodeType": "157", "messageId": "158", "endLine": 417, "endColumn": 40843, "suppressions": "175"}, {"ruleId": "176", "message": "177", "line": 417, "column": 40938, "endLine": 417, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "178"}, {"ruleId": "179", "message": "180", "line": 417, "column": 40938, "endLine": 417, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "181"}, {"ruleId": "182", "message": "183", "line": 417, "column": 40938, "endLine": 417, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "184"}, {"ruleId": "185", "message": "186", "line": 417, "column": 40938, "endLine": 417, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "187"}, {"ruleId": "188", "message": "189", "line": 417, "column": 40938, "endLine": 417, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "190"}, {"ruleId": "191", "message": "192", "line": 417, "column": 40938, "endLine": 417, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "193"}, {"ruleId": "194", "message": "195", "line": 417, "column": 40938, "endLine": 417, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "196"}, {"ruleId": "163", "severity": 1, "message": "164", "line": 316, "column": 100, "nodeType": "157", "messageId": "165", "endLine": 316, "endColumn": 104, "suppressions": "197"}, {"ruleId": "163", "severity": 1, "message": "164", "line": 316, "column": 141, "nodeType": "157", "messageId": "165", "endLine": 316, "endColumn": 145, "suppressions": "198"}, {"ruleId": "155", "severity": 1, "message": "168", "line": 316, "column": 40460, "nodeType": "157", "messageId": "158", "endLine": 316, "endColumn": 40465, "suppressions": "199"}, {"ruleId": "155", "severity": 1, "message": "170", "line": 316, "column": 40594, "nodeType": "157", "messageId": "158", "endLine": 316, "endColumn": 40599, "suppressions": "200"}, {"ruleId": "155", "severity": 1, "message": "172", "line": 316, "column": 40728, "nodeType": "157", "messageId": "158", "endLine": 316, "endColumn": 40733, "suppressions": "201"}, {"ruleId": "155", "severity": 1, "message": "174", "line": 316, "column": 40838, "nodeType": "157", "messageId": "158", "endLine": 316, "endColumn": 40843, "suppressions": "202"}, {"ruleId": "176", "message": "177", "line": 316, "column": 40938, "endLine": 316, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "203"}, {"ruleId": "179", "message": "180", "line": 316, "column": 40938, "endLine": 316, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "204"}, {"ruleId": "182", "message": "183", "line": 316, "column": 40938, "endLine": 316, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "205"}, {"ruleId": "185", "message": "186", "line": 316, "column": 40938, "endLine": 316, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "206"}, {"ruleId": "188", "message": "189", "line": 316, "column": 40938, "endLine": 316, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "207"}, {"ruleId": "191", "message": "192", "line": 316, "column": 40938, "endLine": 316, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "208"}, {"ruleId": "194", "message": "195", "line": 316, "column": 40938, "endLine": 316, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "209"}, {"ruleId": "155", "severity": 1, "message": "210", "line": 1, "column": 38, "nodeType": "157", "messageId": "158", "endLine": 1, "endColumn": 48}, {"ruleId": "163", "severity": 1, "message": "164", "line": 640, "column": 100, "nodeType": "157", "messageId": "165", "endLine": 640, "endColumn": 104, "suppressions": "211"}, {"ruleId": "163", "severity": 1, "message": "164", "line": 640, "column": 141, "nodeType": "157", "messageId": "165", "endLine": 640, "endColumn": 145, "suppressions": "212"}, {"ruleId": "155", "severity": 1, "message": "213", "line": 640, "column": 40328, "nodeType": "157", "messageId": "158", "endLine": 640, "endColumn": 40333, "suppressions": "214"}, {"ruleId": "155", "severity": 1, "message": "168", "line": 640, "column": 40460, "nodeType": "157", "messageId": "158", "endLine": 640, "endColumn": 40465, "suppressions": "215"}, {"ruleId": "155", "severity": 1, "message": "172", "line": 640, "column": 40728, "nodeType": "157", "messageId": "158", "endLine": 640, "endColumn": 40733, "suppressions": "216"}, {"ruleId": "155", "severity": 1, "message": "174", "line": 640, "column": 40838, "nodeType": "157", "messageId": "158", "endLine": 640, "endColumn": 40843, "suppressions": "217"}, {"ruleId": "176", "message": "177", "line": 640, "column": 40938, "endLine": 640, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "218"}, {"ruleId": "179", "message": "180", "line": 640, "column": 40938, "endLine": 640, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "219"}, {"ruleId": "182", "message": "183", "line": 640, "column": 40938, "endLine": 640, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "220"}, {"ruleId": "185", "message": "186", "line": 640, "column": 40938, "endLine": 640, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "221"}, {"ruleId": "188", "message": "189", "line": 640, "column": 40938, "endLine": 640, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "222"}, {"ruleId": "191", "message": "192", "line": 640, "column": 40938, "endLine": 640, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "223"}, {"ruleId": "194", "message": "195", "line": 640, "column": 40938, "endLine": 640, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "224"}, {"ruleId": "163", "severity": 1, "message": "164", "line": 175, "column": 100, "nodeType": "157", "messageId": "165", "endLine": 175, "endColumn": 104, "suppressions": "225"}, {"ruleId": "163", "severity": 1, "message": "164", "line": 175, "column": 141, "nodeType": "157", "messageId": "165", "endLine": 175, "endColumn": 145, "suppressions": "226"}, {"ruleId": "155", "severity": 1, "message": "168", "line": 175, "column": 40460, "nodeType": "157", "messageId": "158", "endLine": 175, "endColumn": 40465, "suppressions": "227"}, {"ruleId": "155", "severity": 1, "message": "170", "line": 175, "column": 40594, "nodeType": "157", "messageId": "158", "endLine": 175, "endColumn": 40599, "suppressions": "228"}, {"ruleId": "155", "severity": 1, "message": "172", "line": 175, "column": 40728, "nodeType": "157", "messageId": "158", "endLine": 175, "endColumn": 40733, "suppressions": "229"}, {"ruleId": "155", "severity": 1, "message": "174", "line": 175, "column": 40838, "nodeType": "157", "messageId": "158", "endLine": 175, "endColumn": 40843, "suppressions": "230"}, {"ruleId": "176", "message": "177", "line": 175, "column": 40938, "endLine": 175, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "231"}, {"ruleId": "179", "message": "180", "line": 175, "column": 40938, "endLine": 175, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "232"}, {"ruleId": "182", "message": "183", "line": 175, "column": 40938, "endLine": 175, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "233"}, {"ruleId": "185", "message": "186", "line": 175, "column": 40938, "endLine": 175, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "234"}, {"ruleId": "188", "message": "189", "line": 175, "column": 40938, "endLine": 175, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "235"}, {"ruleId": "191", "message": "192", "line": 175, "column": 40938, "endLine": 175, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "236"}, {"ruleId": "194", "message": "195", "line": 175, "column": 40938, "endLine": 175, "endColumn": 41205, "severity": 2, "nodeType": null, "suppressions": "237"}, "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'user' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-eval", "eval can be harmful.", "unexpected", ["238", "239"], ["240", "241"], "'oo_tr' is defined but never used.", ["242", "243"], "'oo_tx' is defined but never used.", ["244", "245"], "'oo_ts' is defined but never used.", ["246", "247"], "'oo_te' is defined but never used.", ["248", "249"], "unicorn/no-abusive-eslint-disable", "Definition for rule 'unicorn/no-abusive-eslint-disable' was not found.", ["250", "251"], "eslint-comments/disable-enable-pair", "Definition for rule 'eslint-comments/disable-enable-pair' was not found.", ["252", "253"], "eslint-comments/no-unlimited-disable", "Definition for rule 'eslint-comments/no-unlimited-disable' was not found.", ["254", "255"], "eslint-comments/no-aggregating-enable", "Definition for rule 'eslint-comments/no-aggregating-enable' was not found.", ["256", "257"], "eslint-comments/no-duplicate-disable", "Definition for rule 'eslint-comments/no-duplicate-disable' was not found.", ["258", "259"], "eslint-comments/no-unused-disable", "Definition for rule 'eslint-comments/no-unused-disable' was not found.", ["260", "261"], "eslint-comments/no-unused-enable", "Definition for rule 'eslint-comments/no-unused-enable' was not found.", ["262", "263"], ["264", "265"], ["266", "267"], ["268", "269"], ["270", "271"], ["272", "273"], ["274", "275"], ["276", "277"], ["278", "279"], ["280", "281"], ["282", "283"], ["284", "285"], ["286", "287"], ["288", "289"], "'useContext' is defined but never used.", ["290", "291"], ["292", "293"], "'oo_oo' is defined but never used.", ["294", "295"], ["296", "297"], ["298", "299"], ["300", "301"], ["302", "303"], ["304", "305"], ["306", "307"], ["308", "309"], ["310", "311"], ["312", "313"], ["314", "315"], ["316", "317"], ["318", "319"], ["320", "321"], ["322", "323"], ["324", "325"], ["326", "327"], ["328", "329"], ["330", "331"], ["332", "333"], ["334", "335"], ["336", "337"], ["338", "339"], ["340", "341"], {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, {"kind": "342", "justification": "343"}, "directive", ""]