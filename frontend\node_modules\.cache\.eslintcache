[{"C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\Navbar.js": "4", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Home.js": "5", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\About.js": "6", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\Footer.js": "7", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Login.js": "8", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Jobs.js": "9", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\JobDetails.js": "10", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Contact.js": "11", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Register.js": "12", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Dashboard.js": "13", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Services.js": "14", "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\JobCard.js": "15"}, {"size": 552, "mtime": 1751051019649, "results": "16", "hashOfConfig": "17"}, {"size": 1522, "mtime": 1751051019633, "results": "18", "hashOfConfig": "17"}, {"size": 375, "mtime": 1751051019649, "results": "19", "hashOfConfig": "17"}, {"size": 3311, "mtime": 1751071117640, "results": "20", "hashOfConfig": "17"}, {"size": 9864, "mtime": 1751053073912, "results": "21", "hashOfConfig": "17"}, {"size": 7266, "mtime": 1751314546534, "results": "22", "hashOfConfig": "17"}, {"size": 5170, "mtime": 1751071439954, "results": "23", "hashOfConfig": "17"}, {"size": 2968, "mtime": 1751051717281, "results": "24", "hashOfConfig": "17"}, {"size": 10712, "mtime": 1751314248278, "results": "25", "hashOfConfig": "17"}, {"size": 15223, "mtime": 1751051019649, "results": "26", "hashOfConfig": "17"}, {"size": 12090, "mtime": 1751051019649, "results": "27", "hashOfConfig": "17"}, {"size": 4852, "mtime": 1751051019649, "results": "28", "hashOfConfig": "17"}, {"size": 23952, "mtime": 1751051019649, "results": "29", "hashOfConfig": "17"}, {"size": 7993, "mtime": 1751313911227, "results": "30", "hashOfConfig": "17"}, {"size": 5404, "mtime": 1751051019649, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "x0mttm", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\Navbar.js", ["77", "78", "79"], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\Footer.js", ["80", "81", "82"], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Login.js", ["83", "84", "85"], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Jobs.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\JobDetails.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Contact.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Register.js", ["86", "87", "88"], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Dashboard.js", ["89"], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\pages\\Services.js", [], [], "C:\\Users\\<USER>\\Documents\\primestaffworks\\frontend\\src\\components\\JobCard.js", [], [], {"ruleId": "90", "severity": 1, "message": "91", "line": 1, "column": 17, "nodeType": "92", "messageId": "93", "endLine": 1, "endColumn": 27}, {"ruleId": "90", "severity": 1, "message": "94", "line": 10, "column": 27, "nodeType": "92", "messageId": "93", "endLine": 10, "endColumn": 45}, {"ruleId": "90", "severity": 1, "message": "95", "line": 12, "column": 9, "nodeType": "92", "messageId": "93", "endLine": 12, "endColumn": 17}, {"ruleId": "96", "severity": 1, "message": "97", "line": 125, "column": 15, "nodeType": "98", "endLine": 125, "endColumn": 50}, {"ruleId": "96", "severity": 1, "message": "97", "line": 128, "column": 15, "nodeType": "98", "endLine": 128, "endColumn": 50}, {"ruleId": "96", "severity": 1, "message": "97", "line": 131, "column": 15, "nodeType": "98", "endLine": 131, "endColumn": 50}, {"ruleId": "90", "severity": 1, "message": "91", "line": 1, "column": 27, "nodeType": "92", "messageId": "93", "endLine": 1, "endColumn": 37}, {"ruleId": "90", "severity": 1, "message": "99", "line": 10, "column": 17, "nodeType": "92", "messageId": "93", "endLine": 10, "endColumn": 25}, {"ruleId": "90", "severity": 1, "message": "95", "line": 12, "column": 9, "nodeType": "92", "messageId": "93", "endLine": 12, "endColumn": 17}, {"ruleId": "90", "severity": 1, "message": "91", "line": 1, "column": 27, "nodeType": "92", "messageId": "93", "endLine": 1, "endColumn": 37}, {"ruleId": "90", "severity": 1, "message": "99", "line": 13, "column": 17, "nodeType": "92", "messageId": "93", "endLine": 13, "endColumn": 25}, {"ruleId": "90", "severity": 1, "message": "95", "line": 15, "column": 9, "nodeType": "92", "messageId": "93", "endLine": 15, "endColumn": 17}, {"ruleId": "90", "severity": 1, "message": "91", "line": 1, "column": 38, "nodeType": "92", "messageId": "93", "endLine": 1, "endColumn": 48}, "no-unused-vars", "'useContext' is defined but never used.", "Identifier", "unusedVar", "'setIsAuthenticated' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'setError' is assigned a value but never used."]