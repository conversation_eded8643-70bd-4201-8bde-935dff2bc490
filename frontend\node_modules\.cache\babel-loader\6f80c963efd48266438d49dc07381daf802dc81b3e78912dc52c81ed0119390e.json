{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\primestaffworks\\\\frontend\\\\src\\\\pages\\\\Services.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Services = () => {\n  const services = [{\n    title: \"Temporary Staffing\",\n    icon: \"fa-bolt\",\n    description: \"Quickly fill short-term needs with our pre-screened temporary workers available for immediate placement.\",\n    features: [\"24-48 hour placement guarantee\", \"Flexible durations from 1 day to 6 months\", \"Pre-screened and qualified candidates\", \"Workers' compensation coverage\", \"Payroll and tax management\"],\n    bestFor: \"Seasonal peaks, special projects, unexpected demand, employee absences\"\n  }, {\n    title: \"Direct Hire Placement\",\n    icon: \"fa-calendar\",\n    description: \"Find permanent employees through our rigorous recruitment and vetting process.\",\n    features: [\"90-day replacement guarantee\", \"Comprehensive skills testing\", \"Background and reference checks\", \"Cultural fit assessment\", \"Competitive salary benchmarking\"],\n    bestFor: \"Long-term staffing needs, key positions, leadership roles\"\n  }, {\n    title: \"Temp-to-Hire\",\n    icon: \"fa-exchange\",\n    description: \"Evaluate workers on the job before making a permanent hiring decision.\",\n    features: [\"Low-risk trial period\", \"Seamless transition to permanent\", \"Reduced recruiting costs\", \"Performance evaluations\", \"Flexible conversion timelines\"],\n    bestFor: \"When you want to evaluate fit before permanent commitment\"\n  }, {\n    title: \"On-Site Management\",\n    icon: \"fa-building\",\n    description: \"Our dedicated managers oversee your temporary workforce at your facility.\",\n    features: [\"Dedicated account manager\", \"Daily supervision\", \"Time and attendance tracking\", \"Performance management\", \"Continuous recruitment\"],\n    bestFor: \"Large-scale operations, high-volume staffing needs\"\n  }, {\n    title: \"Payroll Services\",\n    icon: \"fa-calculator\",\n    description: \"Outsource your workforce payroll and administrative tasks to our experts.\",\n    features: [\"Complete payroll processing\", \"Tax filing and compliance\", \"Benefits administration\", \"Workers' compensation\", \"Detailed reporting\"],\n    bestFor: \"Companies wanting to reduce HR overhead\"\n  }, {\n    title: \"Specialized Recruitment\",\n    icon: \"fa-search-plus\",\n    description: \"Targeted search for hard-to-find warehouse and logistics specialists.\",\n    features: [\"Niche talent networks\", \"Competitive market analysis\", \"Passive candidate outreach\", \"Skills testing\", \"Exclusive candidate access\"],\n    bestFor: \"Hard-to-fill positions, specialized skills, leadership roles\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"services-page\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"hero-section bg-primary text-white py-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container py-5\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-6 mb-4 mb-lg-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"display-4 fw-bold mb-4\",\n              children: \"Our Staffing Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"lead\",\n              children: \"Comprehensive solutions tailored to your warehouse and logistics staffing needs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-6\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80\",\n              alt: \"Professional staffing services for warehouse and logistics operations\",\n              className: \"img-fluid rounded shadow-lg opacity-70\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"fw-bold\",\n            children: \"Complete Staffing Solutions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"lead text-muted\",\n            children: \"Flexible options to meet your specific requirements\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row g-4\",\n          children: services.map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-lg-4 col-md-6\"\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5 bg-light\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"fw-bold\",\n            children: \"Our Proven Process\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"lead text-muted\",\n            children: \"How we deliver exceptional staffing results\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row g-4\",\n          children: [{\n            step: \"1\",\n            title: \"Needs Assessment\",\n            desc: \"We thoroughly understand your specific requirements, culture, and challenges\"\n          }, {\n            step: \"2\",\n            title: \"Talent Sourcing\",\n            desc: \"We tap into our extensive network and use targeted recruitment strategies\"\n          }, {\n            step: \"3\",\n            title: \"Rigorous Screening\",\n            desc: \"Candidates undergo skills testing, interviews, and background checks\"\n          }, {\n            step: \"4\",\n            title: \"Presentation\",\n            desc: \"We present only the most qualified candidates that match your needs\"\n          }, {\n            step: \"5\",\n            title: \"Placement\",\n            desc: \"We facilitate interviews and handle all onboarding logistics\"\n          }, {\n            step: \"6\",\n            title: \"Ongoing Support\",\n            desc: \"We provide continuous support to ensure satisfaction on both sides\"\n          }].map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-md-4 col-lg-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card h-100 border-0 bg-white text-center p-3 hover-effect\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3 mx-auto\",\n                style: {\n                  width: \"50px\",\n                  height: \"50px\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"fw-bold\",\n                  children: item.step\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"mb-0\",\n                children: item.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-5 bg-primary text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container text-center py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"fw-bold mb-4\",\n          children: \"Ready to Solve Your Staffing Challenges?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"lead mb-5\",\n          children: \"Our experts are ready to discuss your specific needs and recommend the best solutions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex flex-wrap justify-content-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/contact\",\n            className: \"btn btn-light btn-lg px-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fa fa-phone me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), \" Get Started\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/jobs\",\n            className: \"btn btn-outline-light btn-lg px-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fa fa-file-text me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), \" Request a Proposal\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_c = Services;\nexport default Services;\nvar _c;\n$RefreshReg$(_c, \"Services\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Services", "services", "title", "icon", "description", "features", "bestFor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "map", "service", "index", "step", "desc", "item", "style", "width", "height", "href", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/primestaffworks/frontend/src/pages/Services.js"], "sourcesContent": ["import React from \"react\";\r\nconst Services = () => {\r\n  const services = [\r\n    {\r\n      title: \"Temporary Staffing\",\r\n      icon: \"fa-bolt\",\r\n      description:\r\n        \"Quickly fill short-term needs with our pre-screened temporary workers available for immediate placement.\",\r\n      features: [\r\n        \"24-48 hour placement guarantee\",\r\n        \"Flexible durations from 1 day to 6 months\",\r\n        \"Pre-screened and qualified candidates\",\r\n        \"Workers' compensation coverage\",\r\n        \"Payroll and tax management\",\r\n      ],\r\n      bestFor:\r\n        \"Seasonal peaks, special projects, unexpected demand, employee absences\",\r\n    },\r\n    {\r\n      title: \"Direct Hire Placement\",\r\n      icon: \"fa-calendar\",\r\n      description:\r\n        \"Find permanent employees through our rigorous recruitment and vetting process.\",\r\n      features: [\r\n        \"90-day replacement guarantee\",\r\n        \"Comprehensive skills testing\",\r\n        \"Background and reference checks\",\r\n        \"Cultural fit assessment\",\r\n        \"Competitive salary benchmarking\",\r\n      ],\r\n      bestFor: \"Long-term staffing needs, key positions, leadership roles\",\r\n    },\r\n    {\r\n      title: \"Temp-to-Hire\",\r\n      icon: \"fa-exchange\",\r\n      description:\r\n        \"Evaluate workers on the job before making a permanent hiring decision.\",\r\n      features: [\r\n        \"Low-risk trial period\",\r\n        \"Seamless transition to permanent\",\r\n        \"Reduced recruiting costs\",\r\n        \"Performance evaluations\",\r\n        \"Flexible conversion timelines\",\r\n      ],\r\n      bestFor: \"When you want to evaluate fit before permanent commitment\",\r\n    },\r\n    {\r\n      title: \"On-Site Management\",\r\n      icon: \"fa-building\",\r\n      description:\r\n        \"Our dedicated managers oversee your temporary workforce at your facility.\",\r\n      features: [\r\n        \"Dedicated account manager\",\r\n        \"Daily supervision\",\r\n        \"Time and attendance tracking\",\r\n        \"Performance management\",\r\n        \"Continuous recruitment\",\r\n      ],\r\n      bestFor: \"Large-scale operations, high-volume staffing needs\",\r\n    },\r\n    {\r\n      title: \"Payroll Services\",\r\n      icon: \"fa-calculator\",\r\n      description:\r\n        \"Outsource your workforce payroll and administrative tasks to our experts.\",\r\n      features: [\r\n        \"Complete payroll processing\",\r\n        \"Tax filing and compliance\",\r\n        \"Benefits administration\",\r\n        \"Workers' compensation\",\r\n        \"Detailed reporting\",\r\n      ],\r\n      bestFor: \"Companies wanting to reduce HR overhead\",\r\n    },\r\n    {\r\n      title: \"Specialized Recruitment\",\r\n      icon: \"fa-search-plus\",\r\n      description:\r\n        \"Targeted search for hard-to-find warehouse and logistics specialists.\",\r\n      features: [\r\n        \"Niche talent networks\",\r\n        \"Competitive market analysis\",\r\n        \"Passive candidate outreach\",\r\n        \"Skills testing\",\r\n        \"Exclusive candidate access\",\r\n      ],\r\n      bestFor: \"Hard-to-fill positions, specialized skills, leadership roles\",\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"services-page\">\r\n      {/* Hero Section */}\r\n      <section className=\"hero-section bg-primary text-white py-5\">\r\n        <div className=\"container py-5\">\r\n          <div className=\"row align-items-center\">\r\n            <div className=\"col-lg-6 mb-4 mb-lg-0\">\r\n              <h1 className=\"display-4 fw-bold mb-4\">Our Staffing Services</h1>\r\n              <p className=\"lead\">\r\n                Comprehensive solutions tailored to your warehouse and logistics\r\n                staffing needs\r\n              </p>\r\n            </div>\r\n            <div className=\"col-lg-6\">\r\n              <img\r\n                src=\"https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80\"\r\n                alt=\"Professional staffing services for warehouse and logistics operations\"\r\n                className=\"img-fluid rounded shadow-lg opacity-70\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* All Services */}\r\n      <section className=\"py-5\">\r\n        <div className=\"container\">\r\n          <div className=\"text-center mb-5\">\r\n            <h2 className=\"fw-bold\">Complete Staffing Solutions</h2>\r\n            <p className=\"lead text-muted\">\r\n              Flexible options to meet your specific requirements\r\n            </p>\r\n          </div>\r\n          <div className=\"row g-4\">\r\n            {services.map((service, index) => (\r\n              <div key={index} className=\"col-lg-4 col-md-6\">\r\n                {/* <ServiceCard service={service} /> */}\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Process Section */}\r\n      <section className=\"py-5 bg-light\">\r\n        <div className=\"container\">\r\n          <div className=\"text-center mb-5\">\r\n            <h2 className=\"fw-bold\">Our Proven Process</h2>\r\n            <p className=\"lead text-muted\">\r\n              How we deliver exceptional staffing results\r\n            </p>\r\n          </div>\r\n          <div className=\"row g-4\">\r\n            {[\r\n              {\r\n                step: \"1\",\r\n                title: \"Needs Assessment\",\r\n                desc: \"We thoroughly understand your specific requirements, culture, and challenges\",\r\n              },\r\n              {\r\n                step: \"2\",\r\n                title: \"Talent Sourcing\",\r\n                desc: \"We tap into our extensive network and use targeted recruitment strategies\",\r\n              },\r\n              {\r\n                step: \"3\",\r\n                title: \"Rigorous Screening\",\r\n                desc: \"Candidates undergo skills testing, interviews, and background checks\",\r\n              },\r\n              {\r\n                step: \"4\",\r\n                title: \"Presentation\",\r\n                desc: \"We present only the most qualified candidates that match your needs\",\r\n              },\r\n              {\r\n                step: \"5\",\r\n                title: \"Placement\",\r\n                desc: \"We facilitate interviews and handle all onboarding logistics\",\r\n              },\r\n              {\r\n                step: \"6\",\r\n                title: \"Ongoing Support\",\r\n                desc: \"We provide continuous support to ensure satisfaction on both sides\",\r\n              },\r\n            ].map((item, index) => (\r\n              <div key={index} className=\"col-md-4 col-lg-2\">\r\n                <div className=\"card h-100 border-0 bg-white text-center p-3 hover-effect\">\r\n                  <div\r\n                    className=\"bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3 mx-auto\"\r\n                    style={{ width: \"50px\", height: \"50px\" }}\r\n                  >\r\n                    <span className=\"fw-bold\">{item.step}</span>\r\n                  </div>\r\n                  <h6 className=\"mb-0\">{item.title}</h6>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-5 bg-primary text-white\">\r\n        <div className=\"container text-center py-4\">\r\n          <h2 className=\"fw-bold mb-4\">\r\n            Ready to Solve Your Staffing Challenges?\r\n          </h2>\r\n          <p className=\"lead mb-5\">\r\n            Our experts are ready to discuss your specific needs and recommend\r\n            the best solutions.\r\n          </p>\r\n          <div className=\"d-flex flex-wrap justify-content-center gap-3\">\r\n            <a href=\"/contact\" className=\"btn btn-light btn-lg px-4\">\r\n              <i className=\"fa fa-phone me-2\"></i> Get Started\r\n            </a>\r\n            <a href=\"/jobs\" className=\"btn btn-outline-light btn-lg px-4\">\r\n              <i className=\"fa fa-file-text me-2\"></i> Request a Proposal\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Services;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC1B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,MAAMC,QAAQ,GAAG,CACf;IACEC,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,EAAE,SAAS;IACfC,WAAW,EACT,0GAA0G;IAC5GC,QAAQ,EAAE,CACR,gCAAgC,EAChC,2CAA2C,EAC3C,uCAAuC,EACvC,gCAAgC,EAChC,4BAA4B,CAC7B;IACDC,OAAO,EACL;EACJ,CAAC,EACD;IACEJ,KAAK,EAAE,uBAAuB;IAC9BC,IAAI,EAAE,aAAa;IACnBC,WAAW,EACT,gFAAgF;IAClFC,QAAQ,EAAE,CACR,8BAA8B,EAC9B,8BAA8B,EAC9B,iCAAiC,EACjC,yBAAyB,EACzB,iCAAiC,CAClC;IACDC,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,KAAK,EAAE,cAAc;IACrBC,IAAI,EAAE,aAAa;IACnBC,WAAW,EACT,wEAAwE;IAC1EC,QAAQ,EAAE,CACR,uBAAuB,EACvB,kCAAkC,EAClC,0BAA0B,EAC1B,yBAAyB,EACzB,+BAA+B,CAChC;IACDC,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,EAAE,aAAa;IACnBC,WAAW,EACT,2EAA2E;IAC7EC,QAAQ,EAAE,CACR,2BAA2B,EAC3B,mBAAmB,EACnB,8BAA8B,EAC9B,wBAAwB,EACxB,wBAAwB,CACzB;IACDC,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE,eAAe;IACrBC,WAAW,EACT,2EAA2E;IAC7EC,QAAQ,EAAE,CACR,6BAA6B,EAC7B,2BAA2B,EAC3B,yBAAyB,EACzB,uBAAuB,EACvB,oBAAoB,CACrB;IACDC,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,KAAK,EAAE,yBAAyB;IAChCC,IAAI,EAAE,gBAAgB;IACtBC,WAAW,EACT,uEAAuE;IACzEC,QAAQ,EAAE,CACR,uBAAuB,EACvB,6BAA6B,EAC7B,4BAA4B,EAC5B,gBAAgB,EAChB,4BAA4B,CAC7B;IACDC,OAAO,EAAE;EACX,CAAC,CACF;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5BT,OAAA;MAASQ,SAAS,EAAC,yCAAyC;MAAAC,QAAA,eAC1DT,OAAA;QAAKQ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BT,OAAA;UAAKQ,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCT,OAAA;YAAKQ,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCT,OAAA;cAAIQ,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjEb,OAAA;cAAGQ,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAGpB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNb,OAAA;YAAKQ,SAAS,EAAC,UAAU;YAAAC,QAAA,eACvBT,OAAA;cACEc,GAAG,EAAC,wKAAwK;cAC5KC,GAAG,EAAC,uEAAuE;cAC3EP,SAAS,EAAC;YAAwC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVb,OAAA;MAASQ,SAAS,EAAC,MAAM;MAAAC,QAAA,eACvBT,OAAA;QAAKQ,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBT,OAAA;UAAKQ,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BT,OAAA;YAAIQ,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxDb,OAAA;YAAGQ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAE/B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNb,OAAA;UAAKQ,SAAS,EAAC,SAAS;UAAAC,QAAA,EACrBP,QAAQ,CAACc,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BlB,OAAA;YAAiBQ,SAAS,EAAC;UAAmB,GAApCU,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVb,OAAA;MAASQ,SAAS,EAAC,eAAe;MAAAC,QAAA,eAChCT,OAAA;QAAKQ,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBT,OAAA;UAAKQ,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BT,OAAA;YAAIQ,SAAS,EAAC,SAAS;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/Cb,OAAA;YAAGQ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAE/B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNb,OAAA;UAAKQ,SAAS,EAAC,SAAS;UAAAC,QAAA,EACrB,CACC;YACEU,IAAI,EAAE,GAAG;YACThB,KAAK,EAAE,kBAAkB;YACzBiB,IAAI,EAAE;UACR,CAAC,EACD;YACED,IAAI,EAAE,GAAG;YACThB,KAAK,EAAE,iBAAiB;YACxBiB,IAAI,EAAE;UACR,CAAC,EACD;YACED,IAAI,EAAE,GAAG;YACThB,KAAK,EAAE,oBAAoB;YAC3BiB,IAAI,EAAE;UACR,CAAC,EACD;YACED,IAAI,EAAE,GAAG;YACThB,KAAK,EAAE,cAAc;YACrBiB,IAAI,EAAE;UACR,CAAC,EACD;YACED,IAAI,EAAE,GAAG;YACThB,KAAK,EAAE,WAAW;YAClBiB,IAAI,EAAE;UACR,CAAC,EACD;YACED,IAAI,EAAE,GAAG;YACThB,KAAK,EAAE,iBAAiB;YACxBiB,IAAI,EAAE;UACR,CAAC,CACF,CAACJ,GAAG,CAAC,CAACK,IAAI,EAAEH,KAAK,kBAChBlB,OAAA;YAAiBQ,SAAS,EAAC,mBAAmB;YAAAC,QAAA,eAC5CT,OAAA;cAAKQ,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBACxET,OAAA;gBACEQ,SAAS,EAAC,2GAA2G;gBACrHc,KAAK,EAAE;kBAAEC,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE;gBAAO,CAAE;gBAAAf,QAAA,eAEzCT,OAAA;kBAAMQ,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAEY,IAAI,CAACF;gBAAI;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACNb,OAAA;gBAAIQ,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEY,IAAI,CAAClB;cAAK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC,GATEK,KAAK;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVb,OAAA;MAASQ,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eAC7CT,OAAA;QAAKQ,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCT,OAAA;UAAIQ,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLb,OAAA;UAAGQ,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAGzB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJb,OAAA;UAAKQ,SAAS,EAAC,+CAA+C;UAAAC,QAAA,gBAC5DT,OAAA;YAAGyB,IAAI,EAAC,UAAU;YAACjB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACtDT,OAAA;cAAGQ,SAAS,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,gBACtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJb,OAAA;YAAGyB,IAAI,EAAC,OAAO;YAACjB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAC3DT,OAAA;cAAGQ,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,uBAC1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACa,EAAA,GApNIzB,QAAQ;AAsNd,eAAeA,QAAQ;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}