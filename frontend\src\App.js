import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "./index.css";

// Context
import { AuthProvider } from "./context/authContext";

// Components
import Navbar from "./components/Navbar";
import Footer from "./components/Footer";
import PrivateRoute from "./components/PrivateRoute";

// Pages
import Home from "./pages/Home";
import About from "./pages/About";
import Services from "./pages/Services";
import Jobs from "./pages/Jobs";
import JobDetails from "./pages/JobDetails";
import Contact from "./pages/Contact";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Dashboard from "./pages/Dashboard";
import NotFound from "./utils/NotFound";
function App() {
  // Check for dev tools and disable for console css changes

  // useEffect(() => {
  //   let devToolsOpen = false;
  //   const checkDevTools = () => {
  //     const widthThreshold = window.outerWidth - window.innerWidth > 160;
  //     const heightThreshold = window.outerHeight - window.innerHeight > 160;

  //     if ((widthThreshold || heightThreshold) && !devToolsOpen) {
  //       devToolsOpen = true;
  //       window.location.reload(); // Or take other action
  //     }
  //   };

  //   setInterval(checkDevTools, 1000);
  // }, []);
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Navbar />
          <ToastContainer
            position="top-right"
            autoClose={5000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
          />
          <main className="container-fluid px-0">
            <Routes>
              <Route path="/about" element={<About />} />
              <Route path="/services" element={<Services />} />
              <Route path="/jobs" element={<Jobs />} />
              <Route path="/job-details/:id" element={<JobDetails />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route element={<PrivateRoute />}>
                <Route path="/dashboard" element={<Dashboard />} />
                {/* <Route path="/" exact element={<Home />} /> */}
                <Route path="/" exact element={<Home />} />
                <Route path="*" element={<NotFound />} />
              </Route>
            </Routes>
          </main>
          <Footer />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
