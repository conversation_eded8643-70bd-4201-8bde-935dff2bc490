import { Link } from "react-router-dom";

const JobCard = ({ job }) => {
  // Determine badge color based on job type
  const getBadgeClass = (type) => {
    switch (type.toLowerCase()) {
      case "full-time":
        return "bg-success";
      case "part-time":
        return "bg-info";
      case "temporary":
        return "bg-warning text-dark";
      case "contract":
        return "bg-secondary";
      default:
        return "bg-primary";
    }
  };

  return (
    <div className="card mb-4 border-0 shadow-sm hover-effect">
      <div className="card-body p-4">
        <div className="d-flex flex-column flex-md-row justify-content-between">
          <div className="mb-3 mb-md-0">
            <h3 className="h4 mb-2">{job.title}</h3>
            <div className="d-flex flex-wrap align-items-center mb-2">
              <span className="me-3">
                <i className="fa fa-building text-muted me-2"></i>
                {job.company}
              </span>
              <span className="me-3">
                <i className="fa fa-map-marker text-muted me-2"></i>
                {job.location}
              </span>
            </div>
            <div className="d-flex flex-wrap align-items-center">
              <span className={`badge ${getBadgeClass(job.type)} me-2 mb-2`}>
                {job.type}
              </span>
              <span className="badge bg-light text-dark me-2 mb-2">
                <i className="fa fa-clock-o me-1"></i> {job.shift}
              </span>
              <span className="badge bg-light text-dark me-2 mb-2">
                <i className="fa fa-money me-1"></i> {job.salary}
              </span>
            </div>
          </div>
          <div className="d-flex flex-column align-items-md-end">
            <span className="text-muted small mb-2">
              <i className="fa fa-calendar me-1"></i> Posted {job.posted}
            </span>
            <Link
              to={`/job-details/${job.id}`}
              state={{ job }} // Pass job data through state
              className="btn btn-primary mt-auto"
              onClick={() => window.scrollTo(0, 0)}
            >
              View Details
            </Link>
          </div>
        </div>

        <hr className="my-3" />

        <div className="row">
          <div className="col-md-8">
            <h5 className="h6 text-primary">Job Description:</h5>
            <p className="mb-3">{job.description}</p>

            <h5 className="h6 text-primary">Requirements:</h5>
            <ul className="mb-0">
              {job.requirements.map((req, index) => (
                <li key={index}>{req}</li>
              ))}
            </ul>
          </div>
          <div className="col-md-4 mt-3 mt-md-0">
            <div className="card bg-light h-100">
              <div className="card-body">
                <h5 className="h6 text-primary mb-3">Quick Info:</h5>
                {/* <ul className="list-unstyled small">
                  <li className="mb-2">
                    <i className="fa fa-briefcase text-muted me-2"></i>
                    <strong>Type:</strong> {job.type}
                  </li>
                  <li className="mb-2">
                    <i className="fa fa-clock-o text-muted me-2"></i>
                    <strong>Shift:</strong> {job.shift}
                  </li>
                  <li className="mb-2">
                    <i className="fa fa-money text-muted me-2"></i>
                    <strong>Pay:</strong> {job.salary}
                  </li>
                  <li className="mb-2">
                    <i className="fa fa-map-marker text-muted me-2"></i>
                    <strong>Location:</strong> {job.location}
                  </li>
                  <li>
                    <i className="fa fa-calendar text-muted me-2"></i>
                    <strong>Posted:</strong> {job.posted}
                  </li>
                </ul> */}
                <ul className="list-unstyled">
                  {(job.requirements || []).map((item, index) => (
                    <li key={index} className="mb-2 d-flex">
                      <i className="fa fa-check-circle text-primary me-2 mt-1"></i>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>

                {/* For Responsibilities */}
                <ul className="list-unstyled">
                  {(job.responsibilities || []).map((item, index) => (
                    <li key={index} className="mb-2 d-flex">
                      <i className="fa fa-check-circle text-primary me-2 mt-1"></i>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>

                {/* For Benefits */}
                <ul className="list-unstyled">
                  {(job.benefits || []).map((item, index) => (
                    <li key={index} className="mb-2 d-flex">
                      <i className="fa fa-check-circle text-primary me-2 mt-1"></i>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobCard;
