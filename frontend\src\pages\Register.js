import React, { useState, useContext } from "react";
import { Link, useNavigate } from "react-router-dom";
// import AuthContext from "../context/authContext";

const Register = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    password2: "",
    role: "applicant",
  });
  const [error, setError] = useState(null);
  // const authContext = useContext(AuthContext);
  const navigate = useNavigate();

  const { name, email, password, password2, role } = formData;

  const onChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  // const onSubmit = async (e) => {
  //   e.preventDefault();
  //   if (password !== password2) {
  //     setError("Passwords do not match");
  //     return;
  //   }
  //   try {
  //     await authContext.register({ name, email, password, role });
  //     navigate("/dashboard");
  //   } catch (err) {
  //     setError(err.message || "Registration failed");
  //   }
  // };

  return (
    <div className="container my-5">
      <div className="row justify-content-center">
        <div className="col-md-8 col-lg-6">
          <div className="card shadow-lg mt-5">
            <div className="card-body p-5">
              <h2 className="card-title text-center mb-4">
                <i className="fa fa-user-plus me-2"></i>Register
              </h2>

              {error && (
                <div className="alert alert-danger" role="alert">
                  {error}
                </div>
              )}

              <form>
                <div className="mb-3">
                  <label htmlFor="name" className="form-label">
                    Full Name
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    id="name"
                    name="name"
                    value={name}
                    onChange={onChange}
                    required
                  />
                </div>

                <div className="mb-3">
                  <label htmlFor="email" className="form-label">
                    Email Address
                  </label>
                  <input
                    type="email"
                    className="form-control"
                    id="email"
                    name="email"
                    value={email}
                    onChange={onChange}
                    required
                  />
                </div>

                <div className="mb-3">
                  <label htmlFor="password" className="form-label">
                    Password
                  </label>
                  <input
                    type="password"
                    className="form-control"
                    id="password"
                    name="password"
                    value={password}
                    onChange={onChange}
                    minLength="6"
                    required
                  />
                </div>

                <div className="mb-3">
                  <label htmlFor="password2" className="form-label">
                    Confirm Password
                  </label>
                  <input
                    type="password"
                    className="form-control"
                    id="password2"
                    name="password2"
                    value={password2}
                    onChange={onChange}
                    minLength="6"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="role" className="form-label">
                    I am registering as:
                  </label>
                  <select
                    className="form-select"
                    id="role"
                    name="role"
                    value={role}
                    onChange={onChange}
                  >
                    <option value="applicant">Job Applicant</option>
                    <option value="employer">Employer</option>
                  </select>
                </div>

                <div className="d-grid mb-3">
                  <button type="submit" className="btn btn-primary">
                    Register
                  </button>
                </div>

                <div className="text-center">
                  <Link to="/login" className="text-decoration-none">
                    Already have an account? Login
                  </Link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
