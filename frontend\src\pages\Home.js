import { Link } from "react-router-dom";

const Home = () => {
  // Remote image URL for warehouse team
  const warehouseTeamImage =
    "https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80";

  // Industry data for cleaner mapping
  const industries = [
    {
      icon: "fa-truck",
      title: "Logistics & Distribution",
      desc: "Warehouse associates, forklift operators, and inventory specialists",
    },
    {
      icon: "fa-shopping-cart",
      title: "E-Commerce Fulfillment",
      desc: "Order pickers, packers, and shipping clerks",
    },
    {
      icon: "fa-industry",
      title: "Manufacturing",
      desc: "Production workers, machine operators, and material handlers",
    },
    {
      icon: "fa fa-book",
      title: "Retail Distribution",
      desc: "Stock clerks, receivers, and merchandisers",
    },
    {
      icon: "fa fa-cloud",
      title: "Cold Storage",
      desc: "Freezer warehouse workers and refrigerated handlers",
    },
    {
      icon: "fa fa-hospital-o",
      title: "Pharmaceutical",
      desc: "GMP-compliant warehouse personnel",
    },
  ];

  // Stats data
  const stats = [
    { value: "10,000+", label: "Workers Placed" },
    { value: "50+", label: "Client Companies" },
    { value: "95%", label: "Retention Rate" },
    { value: "100%", label: "Support Available" },
  ];

  return (
    <>
      {/* Hero Section */}
      <section className="hero-section bg-info text-white py-5">
        <div className="container py-5">
          <div className="row align-items-center">
            <div className="col-lg-6 mb-4 mb-lg-0">
              <h1 className="display-4 fw-bold mb-4">
                Fast Staff. Lasting Work.
              </h1>
              <p className="lead mb-4">
                Prime Staff Works specializes in connecting top-tier warehouse
                talent with leading employers across the nation. Our mission is
                to provide fast staffing solutions that result in long-term,
                productive work relationships.
              </p>
              <div className="d-flex flex-wrap gap-3">
                <Link to="/jobs" className="btn btn-light btn-lg px-4">
                  <i className="fa fa-search me-2"></i> Browse Jobs
                </Link>
                <Link
                  to="/contact"
                  className="btn btn-outline-light btn-lg px-4"
                >
                  <i className="fa fa-briefcase me-2"></i> Hire Talent
                </Link>
              </div>
            </div>
            <div className="col-lg-6">
              <img
                src={warehouseTeamImage}
                alt="Warehouse team working together"
                className="img-fluid rounded shadow-lg"
                loading="lazy"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row g-4 text-center">
            {stats.map((stat, index) => (
              <div key={index} className="col-md-3">
                <div className="p-4 bg-white rounded shadow-sm">
                  <h2 className="text-primary fw-bold">{stat.value}</h2>
                  <p className="mb-0">{stat.label}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-5">
        <div className="container">
          <div className="text-center mb-5">
            <h2 className="fw-bold">Our Staffing Solutions</h2>
            <p className="lead text-muted">
              Tailored services to meet your warehouse staffing needs
            </p>
          </div>
          <div className="row g-4">
            <div className="col-md-4">
              <div className="card h-100 border-0 shadow-sm hover-effect">
                <div className="card-body p-4 text-center">
                  <div
                    className="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-4"
                    style={{ width: "80px", height: "80px" }}
                  >
                    <i className="fa fa-bolt fa-3x text-primary"></i>
                  </div>
                  <h4 className="mb-3">Temporary Staffing</h4>
                  <p className="text-muted">
                    Quickly fill seasonal peaks, special projects, or unexpected
                    demand with our pre-screened temporary warehouse staff
                    available on short notice.
                  </p>
                  <Link
                    to="/services"
                    className="btn btn-link text-primary text-decoration-none"
                  >
                    Learn More <i className="fa fa-arrow-right ms-1"></i>
                  </Link>
                </div>
              </div>
            </div>
            <div className="col-md-4">
              <div className="card h-100 border-0 shadow-sm hover-effect">
                <div className="card-body p-4 text-center">
                  <div
                    className="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-4"
                    style={{ width: "80px", height: "80px" }}
                  >
                    <i className="fa fa-calendar fa-3x text-primary"></i>
                  </div>
                  <h4 className="mb-3">Direct Hire</h4>
                  <p className="text-muted">
                    Find quality candidates for permanent positions through our
                    rigorous vetting process that evaluates both skills and
                    cultural fit.
                  </p>
                  <Link
                    to="/services"
                    className="btn btn-link text-primary text-decoration-none"
                  >
                    Learn More <i className="fa fa-arrow-right ms-1"></i>
                  </Link>
                </div>
              </div>
            </div>
            <div className="col-md-4">
              <div className="card h-100 border-0 shadow-sm hover-effect">
                <div className="card-body p-4 text-center">
                  <div
                    className="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-4"
                    style={{ width: "80px", height: "80px" }}
                  >
                    <i className="fa fa-users fa-3x text-primary"></i>
                  </div>
                  <h4 className="mb-3">Team Staffing</h4>
                  <p className="text-muted">
                    Complete teams for large projects with coordinated
                    performance, managed by our experienced on-site supervisors.
                  </p>
                  <Link
                    to="/services"
                    className="btn btn-link text-primary text-decoration-none"
                  >
                    Learn More <i className="fa fa-arrow-right ms-1"></i>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Industries We Serve */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="text-center mb-5">
            <h2 className="fw-bold">Industries We Serve</h2>
            <p className="lead text-muted">
              Expert staffing solutions for these key sectors
            </p>
          </div>
          <div className="row g-4">
            {industries.map((industry, index) => (
              <div key={index} className="col-md-4 col-lg-2">
                <div className="card h-100 border-0 bg-white text-center p-3 hover-effect">
                  <i
                    className={`fa ${industry.icon} fa-2x text-primary mb-3`}
                  ></i>
                  <h6 className="mb-0">{industry.title}</h6>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-5">
        <div className="container">
          <div className="text-center mb-5">
            <h2 className="fw-bold">What Our Clients Say</h2>
            <p className="lead text-muted">
              Trusted by companies and workers nationwide
            </p>
          </div>
          {/* <TestimonialsCarousel /> */}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-5 bg-primary text-white">
        <div className="container text-center py-4">
          <h2 className="fw-bold mb-4">Ready to Transform Your Workforce?</h2>
          <p className="lead mb-5">
            Whether you need reliable staff or are looking for rewarding
            warehouse work,
            <br />
            our team is ready to assist you 24/7.
          </p>
          <div className="d-flex flex-wrap justify-content-center gap-3">
            <Link to="/contact" className="btn btn-light btn-lg px-4">
              <i className="fa fa-phone me-2"></i> Contact Us
            </Link>
            <Link to="/jobs" className="btn btn-outline-light btn-lg px-4">
              <i className="fa fa-search me-2"></i> Browse Jobs
            </Link>
          </div>
        </div>
      </section>
    </>
  );
};

export default Home;
