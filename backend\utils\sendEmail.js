const nodemailer = require("nodemailer");
// const htmlToText = require("html-to-text");

// Transport creation function
function createTransport() {
  if (process.env.NODE_ENV === "production") {
    // Sendgrid
    return nodemailer.createTransport({
      service: "SendGrid",
      auth: {
        user: process.env.SENDGRID_USERNAME,
        pass: process.env.SENDGRID_PASSWORD,
      },
    });
  }

  // Mailtrap for development
  return nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    auth: {
      user: process.env.EMAIL_USERNAME,
      pass: process.env.EMAIL_PASSWORD,
    },
  });
}

// HTML template generation
function generateWelcomeEmail(firstName, url) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Welcome to Prime Staff Works!</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #3498db; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { padding: 20px; background-color: #f9f9f9; border-radius: 0 0 5px 5px; }
        .button { display: inline-block; padding: 10px 20px; background-color: #3498db; color: white; text-decoration: none; border-radius: 5px; margin: 15px 0; }
        .footer { margin-top: 20px; font-size: 12px; text-align: center; color: #777; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Welcome to Prime Staff Works!</h1>
      </div>
      <div class="content">
        <p>Hi ${firstName},</p>
        <p>Welcome to Prime Staff Works, the fastest way to get reliable warehouse staff!</p>
        <p>We're excited to have you on board. Whether you're looking for staff or work, we've got you covered.</p>
        <p>Get started by exploring our dashboard:</p>
        <a href="${url}" class="button">Access Dashboard</a>
        <p>If you need any help, please don't hesitate to contact our support team.</p>
        <div class="footer">
          <p>&copy; ${new Date().getFullYear()} Prime Staff Works. All rights reserved.</p>
          <p>Fast Staff. Lasting Work.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

function generatePasswordResetEmail(firstName, url) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Password Reset Request</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #e74c3c; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { padding: 20px; background-color: #f9f9f9; border-radius: 0 0 5px 5px; }
        .button { display: inline-block; padding: 10px 20px; background-color: #e74c3c; color: white; text-decoration: none; border-radius: 5px; margin: 15px 0; }
        .footer { margin-top: 20px; font-size: 12px; text-align: center; color: #777; }
        .note { font-size: 12px; color: #777; margin-top: 20px; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Password Reset Request</h1>
      </div>
      <div class="content">
        <p>Hi ${firstName},</p>
        <p>You requested a password reset for your Prime Staff Works account.</p>
        <p>Click the button below to reset your password (valid for 10 minutes):</p>
        <a href="${url}" class="button">Reset Password</a>
        <p class="note">If you didn't request this, please ignore this email.</p>
        <div class="footer">
          <p>&copy; ${new Date().getFullYear()} Prime Staff Works. All rights reserved.</p>
          <p>Fast Staff. Lasting Work.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

function generateJobConfirmationEmail(firstName, url) {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Job Request Received</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #2ecc71; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
        .content { padding: 20px; background-color: #f9f9f9; border-radius: 0 0 5px 5px; }
        .button { display: inline-block; padding: 10px 20px; background-color: #2ecc71; color: white; text-decoration: none; border-radius: 5px; margin: 15px 0; }
        .footer { margin-top: 20px; font-size: 12px; text-align: center; color: #777; }
        .contact { margin-top: 20px; padding: 10px; background-color: #e8f5e9; border-radius: 5px; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Job Request Received</h1>
      </div>
      <div class="content">
        <p>Hi ${firstName},</p>
        <p>Thank you for submitting your warehouse staffing request to Prime Staff Works!</p>
        <p>We've received your job details and our team is working to match you with qualified staff.</p>
        <p>You can view and manage your request here:</p>
        <a href="${url}" class="button">View Job Request</a>
        <div class="contact">
          <p>We typically respond within 24 hours with candidate matches.</p>
          <p>If you have any urgent needs, please call our support line at (555) 123-4567.</p>
        </div>
        <div class="footer">
          <p>&copy; ${new Date().getFullYear()} Prime Staff Works. All rights reserved.</p>
          <p>Fast Staff. Lasting Work.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Email sending functions
async function sendWelcomeEmail(user, url) {
  const html = generateWelcomeEmail(user.name.split(" ")[0], url);
  const text = htmlToText.convert(html);

  await sendEmail({
    to: user.email,
    subject: "Welcome to Prime Staff Works!",
    html,
    text,
    from: `Prime Staff Works <${process.env.EMAIL_FROM}>`,
  });
}

async function sendPasswordResetEmail(user, url) {
  const html = generatePasswordResetEmail(user.name.split(" ")[0], url);
  const text = htmlToText.convert(html);

  await sendEmail({
    to: user.email,
    subject: "Your password reset token (valid for only 10 minutes)",
    html,
    text,
    from: `Prime Staff Works <${process.env.EMAIL_FROM}>`,
  });
}

async function sendJobConfirmationEmail(user, url) {
  const html = generateJobConfirmationEmail(user.name.split(" ")[0], url);
  const text = htmlToText.convert(html);

  await sendEmail({
    to: user.email,
    subject: "Your warehouse staffing request has been received",
    html,
    text,
    from: `Prime Staff Works <${process.env.EMAIL_FROM}>`,
  });
}

// Core email sending function
async function sendEmail(mailOptions) {
  const transport = createTransport();
  await transport.sendMail(mailOptions);
}

// Export all functions
module.exports = {
  sendWelcomeEmail,
  sendPasswordResetEmail,
  sendJobConfirmationEmail,
  sendEmail,
  generateWelcomeEmail,
  generatePasswordResetEmail,
  generateJobConfirmationEmail,
  createTransport,
};
