// import React from "react";
// import TeamMember from "../components/TeamMember";

const About = () => {
  return (
    <div className="about-page">
      {/* Hero Section */}
      <section className="hero-section  bg-primary text-white py-5">
        <div className="container py-5">
          <div className="row align-items-center">
            <div className="col-lg-8 mb-4 mb-lg-0">
              <h1 className="display-4 fw-bold mb-4">
                About Prime Staff Works
              </h1>
              <p className="lead">
                Delivering exceptional warehouse staffing solutions since 2020
              </p>
            </div>
            {/* <div className="col-lg-6">
              <img
                src="/images/about-hero.jpg"
                alt="Prime Staff Works team"
                className="img-fluid rounded shadow-lg"
              />
            </div> */}
          </div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-5">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-6 mb-4 mb-lg-0">
              <img
                // src="/images/warehouse-team.jpg"
                src="https://plus.unsplash.com/premium_photo-1663090614415-a724fe8e2a92?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                alt="Our team in action"
                className="img-fluid rounded shadow"
              />
            </div>
            <div className="col-lg-6">
              <h2 className="fw-bold mb-4">Our Story</h2>
              <p>
                <i>Prime Staff Works</i> launched with a clear goal: to deliver
                fast, dependable staffing solutions tailored for the warehouse
                and logistics industry. What began as a small regional agency
                has grown into a trusted national partner, known for our
                commitment to speed, quality, and integrity.
              </p>
              <p>
                {" "}
                Today, we serve hundreds of businesses across the
                country—matching them with thousands of reliable, skilled
                warehouse professionals each year. Our growth is driven by
                strong relationships, deep industry knowledge, and a passion for
                helping clients meet their workforce needs efficiently.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Values */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="text-center mb-5">
            <h1 className="fw-bold">Our Mission & Values</h1>
            <p className="lead text-muted">
              The principles that guide everything we do
            </p>
          </div>
          <div className="row g-4">
            {[
              {
                icon: "fa-bolt",
                title: "Speed",
                desc: "We respond quickly to staffing needs, often filling positions within 24 hours",
              },
              {
                icon: "fa-thumbs-up",
                title: "Quality",
                desc: "Our rigorous screening ensures we provide only the most qualified candidates",
              },
              {
                icon: "fa-handshake-o",
                title: "Integrity",
                desc: "We build relationships based on honesty, transparency and trust",
              },
              {
                icon: "fa-line-chart",
                title: "Results",
                desc: "We measure success by our clients' satisfaction and workers' success",
              },
              {
                icon: "fa-heart",
                title: "Care",
                desc: "We treat both clients and workers with respect and understanding",
              },
              {
                icon: "fa-lightbulb-o",
                title: "Innovation",
                desc: "We continuously improve our processes to deliver better solutions",
              },
            ].map((item, index) => (
              <div key={index} className="col-md-4">
                <div className="card h-100 border-0 shadow-sm hover-effect">
                  <div className="card-body p-4 text-center">
                    <i
                      className={`fa ${item.icon} fa-3x text-primary mb-3`}
                    ></i>
                    <h4 className="mb-3">{item.title}</h4>
                    <p className="text-muted mb-0">{item.desc}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Team */}
      <section className="py-5">
        <div className="container">
          <div className="text-center mb-5">
            <h2 className="fw-bold">Meet Our Leadership Team</h2>
            <p className="lead text-muted">
              The experienced professionals guiding our company
            </p>
          </div>
          {/* <div className="row g-4">
            <TeamMember
              name="Robert Johnson"
              position="CEO & Founder"
              bio="With over 25 years in logistics and staffing, Robert founded Prime Staff Works to revolutionize warehouse staffing."
              img="/images/team1.jpg"
            />
            <TeamMember
              name="Sarah Williams"
              position="COO"
              bio="Sarah oversees operations with a focus on efficiency and client satisfaction."
              img="/images/team2.jpg"
            />
            <TeamMember
              name="Michael Chen"
              position="Director of Recruitment"
              bio="Michael leads our talent acquisition team, ensuring we find the best candidates."
              img="/images/team3.jpg"
            />
            <TeamMember
              name="Emily Rodriguez"
              position="Client Relations"
              bio="Emily builds and maintains our strong relationships with client companies."
              img="/images/team4.jpg"
            />
          </div> */}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-5 bg-primary text-white">
        <div className="container text-center py-4">
          <h2 className="fw-bold mb-4">Join the Prime Staff Works Family</h2>
          <p className="lead mb-5">
            Whether you're looking for talent or looking for work, we'd love to
            connect with you.
          </p>
          <div className="d-flex flex-wrap justify-content-center gap-3">
            <a href="/contact" className="btn btn-light btn-lg px-4">
              <i className="fa fa-phone me-2"></i> Contact Us
            </a>
            <a href="/jobs" className="btn btn-outline-light btn-lg px-4">
              <i className="fa fa-search me-2"></i> Browse Jobs
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
