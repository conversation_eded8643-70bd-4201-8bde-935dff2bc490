{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\primestaffworks\\\\frontend\\\\src\\\\App.js\";\nimport { BrowserRouter as Router, Route, Routes } from \"react-router-dom\";\nimport { ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\nimport \"./index.css\";\n\n// Context\nimport { AuthProvider } from \"./context/authContext\";\n\n// Components\nimport Navbar from \"./components/Navbar\";\nimport Footer from \"./components/Footer\";\nimport PrivateRoute from \"./components/PrivateRoute\";\n\n// Pages\nimport Home from \"./pages/Home\";\nimport About from \"./pages/About\";\nimport Services from \"./pages/Services\";\nimport Jobs from \"./pages/Jobs\";\nimport JobDetails from \"./pages/JobDetails\";\nimport Contact from \"./pages/Contact\";\nimport Login from \"./pages/Login\";\nimport Register from \"./pages/Register\";\nimport Dashboard from \"./pages/Dashboard\";\nimport NotFound from \"./utils/NotFound\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  // Check for dev tools and disable for console css changes\n\n  // useEffect(() => {\n  //   let devToolsOpen = false;\n  //   const checkDevTools = () => {\n  //     const widthThreshold = window.outerWidth - window.innerWidth > 160;\n  //     const heightThreshold = window.outerHeight - window.innerHeight > 160;\n\n  //     if ((widthThreshold || heightThreshold) && !devToolsOpen) {\n  //       devToolsOpen = true;\n  //       window.location.reload(); // Or take other action\n  //     }\n  //   };\n\n  //   setInterval(checkDevTools, 1000);\n  // }, []);\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n          position: \"top-right\",\n          autoClose: 5000,\n          hideProgressBar: false,\n          newestOnTop: false,\n          closeOnClick: true,\n          rtl: false,\n          pauseOnFocusLoss: true,\n          draggable: true,\n          pauseOnHover: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"container-fluid px-0\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/about\",\n              element: /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/services\",\n              element: /*#__PURE__*/_jsxDEV(Services, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/jobs\",\n              element: /*#__PURE__*/_jsxDEV(Jobs, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 44\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/job-details/:id\",\n              element: /*#__PURE__*/_jsxDEV(JobDetails, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 55\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/contact\",\n              element: /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/register\",\n              element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              element: /*#__PURE__*/_jsxDEV(PrivateRoute, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 31\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/dashboard\",\n                element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 68,\n                  columnNumber: 51\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                exact: true,\n                element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 70,\n                  columnNumber: 48\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"*\",\n                element: /*#__PURE__*/_jsxDEV(NotFound, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Route", "Routes", "ToastContainer", "<PERSON>th<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Footer", "PrivateRoute", "Home", "About", "Services", "Jobs", "JobDetails", "Contact", "<PERSON><PERSON>", "Register", "Dashboard", "NotFound", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "path", "element", "exact", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/primestaffworks/frontend/src/App.js"], "sourcesContent": ["import { BrowserRouter as Router, Route, Routes } from \"react-router-dom\";\r\nimport { ToastContainer } from \"react-toastify\";\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\nimport \"./index.css\";\r\n\r\n// Context\r\nimport { AuthProvider } from \"./context/authContext\";\r\n\r\n// Components\r\nimport Navbar from \"./components/Navbar\";\r\nimport Footer from \"./components/Footer\";\r\nimport PrivateRoute from \"./components/PrivateRoute\";\r\n\r\n// Pages\r\nimport Home from \"./pages/Home\";\r\nimport About from \"./pages/About\";\r\nimport Services from \"./pages/Services\";\r\nimport Jobs from \"./pages/Jobs\";\r\nimport JobDetails from \"./pages/JobDetails\";\r\nimport Contact from \"./pages/Contact\";\r\nimport Login from \"./pages/Login\";\r\nimport Register from \"./pages/Register\";\r\nimport Dashboard from \"./pages/Dashboard\";\r\nimport NotFound from \"./utils/NotFound\";\r\nfunction App() {\r\n  // Check for dev tools and disable for console css changes\r\n\r\n  // useEffect(() => {\r\n  //   let devToolsOpen = false;\r\n  //   const checkDevTools = () => {\r\n  //     const widthThreshold = window.outerWidth - window.innerWidth > 160;\r\n  //     const heightThreshold = window.outerHeight - window.innerHeight > 160;\r\n\r\n  //     if ((widthThreshold || heightThreshold) && !devToolsOpen) {\r\n  //       devToolsOpen = true;\r\n  //       window.location.reload(); // Or take other action\r\n  //     }\r\n  //   };\r\n\r\n  //   setInterval(checkDevTools, 1000);\r\n  // }, []);\r\n  return (\r\n    <AuthProvider>\r\n      <Router>\r\n        <div className=\"App\">\r\n          <Navbar />\r\n          <ToastContainer\r\n            position=\"top-right\"\r\n            autoClose={5000}\r\n            hideProgressBar={false}\r\n            newestOnTop={false}\r\n            closeOnClick\r\n            rtl={false}\r\n            pauseOnFocusLoss\r\n            draggable\r\n            pauseOnHover\r\n          />\r\n          <main className=\"container-fluid px-0\">\r\n            <Routes>\r\n              <Route path=\"/about\" element={<About />} />\r\n              <Route path=\"/services\" element={<Services />} />\r\n              <Route path=\"/jobs\" element={<Jobs />} />\r\n              <Route path=\"/job-details/:id\" element={<JobDetails />} />\r\n              <Route path=\"/contact\" element={<Contact />} />\r\n              <Route path=\"/login\" element={<Login />} />\r\n              <Route path=\"/register\" element={<Register />} />\r\n              <Route element={<PrivateRoute />}>\r\n                <Route path=\"/dashboard\" element={<Dashboard />} />\r\n                {/* <Route path=\"/\" exact element={<Home />} /> */}\r\n                <Route path=\"/\" exact element={<Home />} />\r\n                <Route path=\"*\" element={<NotFound />} />\r\n              </Route>\r\n            </Routes>\r\n          </main>\r\n          <Footer />\r\n        </div>\r\n      </Router>\r\n    </AuthProvider>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,kBAAkB;AACzE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAC9C,OAAO,aAAa;;AAEpB;AACA,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,YAAY,MAAM,2BAA2B;;AAEpD;AACA,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,QAAQ,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACxC,SAASC,GAAGA,CAAA,EAAG;EACb;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA,oBACED,OAAA,CAACf,YAAY;IAAAiB,QAAA,eACXF,OAAA,CAACnB,MAAM;MAAAqB,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,KAAK;QAAAD,QAAA,gBAClBF,OAAA,CAACd,MAAM;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVP,OAAA,CAAChB,cAAc;UACbwB,QAAQ,EAAC,WAAW;UACpBC,SAAS,EAAE,IAAK;UAChBC,eAAe,EAAE,KAAM;UACvBC,WAAW,EAAE,KAAM;UACnBC,YAAY;UACZC,GAAG,EAAE,KAAM;UACXC,gBAAgB;UAChBC,SAAS;UACTC,YAAY;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACFP,OAAA;UAAMG,SAAS,EAAC,sBAAsB;UAAAD,QAAA,eACpCF,OAAA,CAACjB,MAAM;YAAAmB,QAAA,gBACLF,OAAA,CAAClB,KAAK;cAACmC,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAElB,OAAA,CAACV,KAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CP,OAAA,CAAClB,KAAK;cAACmC,IAAI,EAAC,WAAW;cAACC,OAAO,eAAElB,OAAA,CAACT,QAAQ;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDP,OAAA,CAAClB,KAAK;cAACmC,IAAI,EAAC,OAAO;cAACC,OAAO,eAAElB,OAAA,CAACR,IAAI;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzCP,OAAA,CAAClB,KAAK;cAACmC,IAAI,EAAC,kBAAkB;cAACC,OAAO,eAAElB,OAAA,CAACP,UAAU;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DP,OAAA,CAAClB,KAAK;cAACmC,IAAI,EAAC,UAAU;cAACC,OAAO,eAAElB,OAAA,CAACN,OAAO;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CP,OAAA,CAAClB,KAAK;cAACmC,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAElB,OAAA,CAACL,KAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CP,OAAA,CAAClB,KAAK;cAACmC,IAAI,EAAC,WAAW;cAACC,OAAO,eAAElB,OAAA,CAACJ,QAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDP,OAAA,CAAClB,KAAK;cAACoC,OAAO,eAAElB,OAAA,CAACZ,YAAY;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAL,QAAA,gBAC/BF,OAAA,CAAClB,KAAK;gBAACmC,IAAI,EAAC,YAAY;gBAACC,OAAO,eAAElB,OAAA,CAACH,SAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEnDP,OAAA,CAAClB,KAAK;gBAACmC,IAAI,EAAC,GAAG;gBAACE,KAAK;gBAACD,OAAO,eAAElB,OAAA,CAACX,IAAI;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CP,OAAA,CAAClB,KAAK;gBAACmC,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAElB,OAAA,CAACF,QAAQ;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACPP,OAAA,CAACb,MAAM;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACa,EAAA,GAvDQnB,GAAG;AAyDZ,eAAeA,GAAG;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}